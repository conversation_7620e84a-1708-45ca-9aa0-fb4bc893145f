import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userRole = ref.watch(userRoleProvider);
    final userData = ref.watch(currentUserDataProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await ref.read(authControllerProvider.notifier).signOut();
              if (context.mounted) {
                Navigator.of(context).pushReplacementNamed('/login');
              }
            },
          ),
        ],
      ),
      body: userRole.when(
        data: (role) {
          if (role == null) {
            return const Center(
              child: Text('Erreur: Rôle utilisateur non trouvé'),
            );
          }

          return userData.when(
            data: (data) {
              if (data == null) {
                return const Center(
                  child: Text('Erreur: Donn<PERSON> utilisateur non trouvées'),
                );
              }

              return _buildDashboardContent(context, role, data);
            },
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stack) => Center(
              child: Text('Erreur: $error'),
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Text('Erreur: $error'),
        ),
      ),
    );
  }

  Widget _buildDashboardContent(
    BuildContext context,
    String role,
    Map<String, dynamic> userData,
  ) {
    switch (role) {
      case AppConstants.commercialRole:
        return _buildCommercialDashboard(context, userData);
      case AppConstants.merchandizerRole:
        return _buildMerchandizerDashboard(context, userData);
      case AppConstants.adminRole:
        return _buildAdminDashboard(context, userData);
      default:
        return const Center(
          child: Text('Rôle non reconnu'),
        );
    }
  }

  Widget _buildCommercialDashboard(
    BuildContext context,
    Map<String, dynamic> userData,
  ) {
    final commercial = userData['data'];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bienvenue, ${commercial.fullName}',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Commercial - ${commercial.territory}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(AppColors.secondaryTextColorValue),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Quick Actions
          Text(
            'Actions rapides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          
          const SizedBox(height: 16),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildActionCard(
                context,
                'Commandes',
                Icons.shopping_cart,
                const Color(AppColors.primaryColorValue),
                () {
                  // TODO: Navigate to orders
                },
              ),
              _buildActionCard(
                context,
                'Devis',
                Icons.description,
                const Color(AppColors.secondaryColorValue),
                () {
                  // TODO: Navigate to quotes
                },
              ),
              _buildActionCard(
                context,
                'Clients',
                Icons.people,
                const Color(AppColors.infoColorValue),
                () {
                  // TODO: Navigate to clients
                },
              ),
              _buildActionCard(
                context,
                'Missions',
                Icons.assignment,
                const Color(AppColors.warningColorValue),
                () {
                  // TODO: Navigate to missions
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMerchandizerDashboard(
    BuildContext context,
    Map<String, dynamic> userData,
  ) {
    final merchandizer = userData['data'];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bienvenue, ${merchandizer.fullName}',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Merchandiseur - ${merchandizer.territory}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(AppColors.secondaryTextColorValue),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Superviseur: ${merchandizer.managerName}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(AppColors.secondaryTextColorValue),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Quick Actions
          Text(
            'Actions rapides',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          
          const SizedBox(height: 16),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildActionCard(
                context,
                'Missions du jour',
                Icons.today,
                const Color(AppColors.primaryColorValue),
                () {
                  // TODO: Navigate to today's missions
                },
              ),
              _buildActionCard(
                context,
                'Rapports',
                Icons.report,
                const Color(AppColors.secondaryColorValue),
                () {
                  // TODO: Navigate to reports
                },
              ),
              _buildActionCard(
                context,
                'Calendrier',
                Icons.calendar_today,
                const Color(AppColors.infoColorValue),
                () {
                  // TODO: Navigate to calendar
                },
              ),
              _buildActionCard(
                context,
                'Profil',
                Icons.person,
                const Color(AppColors.warningColorValue),
                () {
                  // TODO: Navigate to profile
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdminDashboard(
    BuildContext context,
    Map<String, dynamic> userData,
  ) {
    return const Center(
      child: Text(
        'Tableau de bord administrateur\n(À implémenter)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

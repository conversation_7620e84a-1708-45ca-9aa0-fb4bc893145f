import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart';
import '../../models/mission_models.dart';
import '../../providers/auth_provider.dart';
import '../../providers/commercial_provider.dart';
import '../../services/navigation_service.dart';
import '../../widgets/cards/performance_card.dart';
import '../../widgets/cards/quick_action_card.dart';
import '../../widgets/cards/recent_items_card.dart';

class CommercialDashboardScreen extends ConsumerWidget {
  const CommercialDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final commercialData = ref.watch(commercialDataProvider);
    final performance = ref.watch(commercialPerformanceProvider);
    final recentOrders = ref.watch(recentOrdersProvider);
    final recentQuotes = ref.watch(recentQuotesProvider);
    final todayMissions = ref.watch(todayMissionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(commercialOrdersProvider);
              ref.invalidate(commercialQuotesProvider);
              ref.invalidate(commercialMissionsProvider);
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await ref.read(authControllerProvider.notifier).signOut();
              if (context.mounted) {
                Navigator.of(context).pushReplacementNamed('/login');
              }
            },
          ),
        ],
      ),
      body: commercialData.when(
        data: (data) {
          if (data == null) {
            return const Center(
              child: Text('Erreur: Données commercial non trouvées'),
            );
          }

          final commercial = data;

          return RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(commercialOrdersProvider);
              ref.invalidate(commercialQuotesProvider);
              ref.invalidate(commercialMissionsProvider);
            },
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Welcome Card
                  Card(
                    child: Padding(
                      padding:
                          const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 30,
                            backgroundColor:
                                const Color(AppColors.primaryColorValue),
                            child: Text(
                              commercial.firstName[0] + commercial.lastName[0],
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Bonjour, ${commercial.firstName}!',
                                  style:
                                      Theme.of(context).textTheme.headlineSmall,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Commercial - ${commercial.territory}',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium
                                      ?.copyWith(
                                        color: const Color(
                                            AppColors.secondaryTextColorValue),
                                      ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '${commercial.assignedClients.length} clients assignés',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: const Color(
                                            AppColors.secondaryTextColorValue),
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Performance Metrics
                  Text(
                    'Performance',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),

                  const SizedBox(height: 12),

                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 1.5,
                    children: [
                      PerformanceCard(
                        title: 'Commandes',
                        value: performance.totalOrders.toString(),
                        subtitle:
                            '${performance.totalRevenue.toStringAsFixed(0)}€',
                        icon: Icons.shopping_cart,
                        color: const Color(AppColors.primaryColorValue),
                      ),
                      PerformanceCard(
                        title: 'Devis',
                        value: performance.totalQuotes.toString(),
                        subtitle:
                            '${performance.quoteConversionRate.toStringAsFixed(1)}% convertis',
                        icon: Icons.description,
                        color: const Color(AppColors.secondaryColorValue),
                      ),
                      PerformanceCard(
                        title: 'Missions',
                        value: performance.totalMissions.toString(),
                        subtitle:
                            '${performance.missionCompletionRate.toStringAsFixed(1)}% complétées',
                        icon: Icons.assignment,
                        color: const Color(AppColors.infoColorValue),
                      ),
                      PerformanceCard(
                        title: 'Clients actifs',
                        value: performance.activeClients.toString(),
                        subtitle:
                            'Panier moyen: ${performance.averageOrderValue.toStringAsFixed(0)}€',
                        icon: Icons.people,
                        color: const Color(AppColors.warningColorValue),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Quick Actions
                  Text(
                    'Actions rapides',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),

                  const SizedBox(height: 12),

                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: 3,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    children: [
                      QuickActionCard(
                        title: 'Nouvelle\nCommande',
                        icon: Icons.add_shopping_cart,
                        color: const Color(AppColors.primaryColorValue),
                        onTap: () => NavigationService.navigateToOrders(),
                      ),
                      QuickActionCard(
                        title: 'Nouveau\nDevis',
                        icon: Icons.note_add,
                        color: const Color(AppColors.secondaryColorValue),
                        onTap: () => NavigationService.navigateToQuotes(),
                      ),
                      QuickActionCard(
                        title: 'Nouvelle\nMission',
                        icon: Icons.assignment_add,
                        color: const Color(AppColors.infoColorValue),
                        onTap: () =>
                            NavigationService.navigateToCommercialMissions(),
                      ),
                      QuickActionCard(
                        title: 'Mes\nClients',
                        icon: Icons.people,
                        color: const Color(AppColors.warningColorValue),
                        onTap: () => NavigationService.navigateToClients(),
                      ),
                      QuickActionCard(
                        title: 'Mes\nMerchandiseurs',
                        icon: Icons.supervisor_account,
                        color: Colors.purple,
                        onTap: () {
                          // TODO: Navigate to merchandizers
                        },
                      ),
                      QuickActionCard(
                        title: 'Calendrier',
                        icon: Icons.calendar_today,
                        color: Colors.teal,
                        onTap: () => NavigationService.navigateToCalendar(),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Today's Missions
                  if (todayMissions.isNotEmpty) ...[
                    Text(
                      'Missions d\'aujourd\'hui (${todayMissions.length})',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 12),
                    Card(
                      child: ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: todayMissions.length,
                        separatorBuilder: (context, index) =>
                            const Divider(height: 1),
                        itemBuilder: (context, index) {
                          final mission = todayMissions[index];
                          return ListTile(
                            leading: CircleAvatar(
                              backgroundColor:
                                  _getMissionStatusColor(mission.status),
                              child: Icon(
                                _getMissionStatusIcon(mission.status),
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            title: Text(mission.title),
                            subtitle: Text(
                              '${mission.merchandizerName} - ${mission.clientName}',
                            ),
                            trailing: Text(
                              '${mission.startTime} - ${mission.endTime}',
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            onTap: () {
                              // TODO: Navigate to mission details
                            },
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Recent Items
                  Row(
                    children: [
                      Expanded(
                        child: RecentItemsCard(
                          title: 'Commandes récentes',
                          items: recentOrders
                              .map((order) => {
                                    'title': order.clientName,
                                    'subtitle':
                                        '${order.totalAmount.toStringAsFixed(0)}€',
                                    'trailing':
                                        _getOrderStatusText(order.status),
                                    'color': _getOrderStatusColor(order.status),
                                  })
                              .toList(),
                          onViewAll: () => NavigationService.navigateToOrders(),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: RecentItemsCard(
                          title: 'Devis récents',
                          items: recentQuotes
                              .map((quote) => {
                                    'title': quote.clientName,
                                    'subtitle':
                                        '${quote.totalAmount.toStringAsFixed(0)}€',
                                    'trailing':
                                        _getQuoteStatusText(quote.status),
                                    'color': _getQuoteStatusColor(quote.status),
                                  })
                              .toList(),
                          onViewAll: () => NavigationService.navigateToQuotes(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Color(AppColors.errorColorValue),
              ),
              const SizedBox(height: 16),
              Text(
                'Erreur de chargement',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getMissionStatusColor(MissionStatus status) {
    switch (status) {
      case MissionStatus.pending:
        return const Color(AppColors.warningColorValue);
      case MissionStatus.inProgress:
        return const Color(AppColors.infoColorValue);
      case MissionStatus.completed:
        return const Color(AppColors.successColorValue);
      case MissionStatus.cancelled:
        return const Color(AppColors.errorColorValue);
    }
  }

  IconData _getMissionStatusIcon(MissionStatus status) {
    switch (status) {
      case MissionStatus.pending:
        return Icons.schedule;
      case MissionStatus.inProgress:
        return Icons.play_arrow;
      case MissionStatus.completed:
        return Icons.check;
      case MissionStatus.cancelled:
        return Icons.close;
    }
  }

  String _getOrderStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'En attente';
      case OrderStatus.confirmed:
        return 'Confirmée';
      case OrderStatus.processing:
        return 'En cours';
      case OrderStatus.shipped:
        return 'Expédiée';
      case OrderStatus.delivered:
        return 'Livrée';
      case OrderStatus.cancelled:
        return 'Annulée';
    }
  }

  Color _getOrderStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return const Color(AppColors.warningColorValue);
      case OrderStatus.confirmed:
      case OrderStatus.processing:
        return const Color(AppColors.infoColorValue);
      case OrderStatus.shipped:
      case OrderStatus.delivered:
        return const Color(AppColors.successColorValue);
      case OrderStatus.cancelled:
        return const Color(AppColors.errorColorValue);
    }
  }

  String _getQuoteStatusText(QuoteStatus status) {
    switch (status) {
      case QuoteStatus.pending:
        return 'En attente';
      case QuoteStatus.sent:
        return 'Envoyé';
      case QuoteStatus.viewed:
        return 'Vu';
      case QuoteStatus.accepted:
        return 'Accepté';
      case QuoteStatus.rejected:
        return 'Refusé';
      case QuoteStatus.expired:
        return 'Expiré';
    }
  }

  Color _getQuoteStatusColor(QuoteStatus status) {
    switch (status) {
      case QuoteStatus.pending:
        return const Color(AppColors.warningColorValue);
      case QuoteStatus.sent:
        return const Color(AppColors.infoColorValue);
      case QuoteStatus.viewed:
        return const Color(AppColors.primaryColorValue);
      case QuoteStatus.accepted:
        return const Color(AppColors.successColorValue);
      case QuoteStatus.rejected:
      case QuoteStatus.expired:
        return const Color(AppColors.errorColorValue);
    }
  }
}

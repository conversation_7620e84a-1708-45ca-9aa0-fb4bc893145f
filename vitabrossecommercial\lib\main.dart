import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'config/firebase_options.dart';
import 'config/app_theme.dart';
import 'constants/app_constants.dart';
import 'services/navigation_service.dart';
import 'screens/auth/login_screen.dart';
import 'screens/shared/splash_screen.dart';
import 'screens/shared/dashboard_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Initialize SharedPreferences
  await SharedPreferences.getInstance();

  runApp(
    const ProviderScope(
      child: <PERSON><PERSON><PERSON><PERSON>A<PERSON>(),
    ),
  );
}

class VitaBrosseApp extends StatelessWidget {
  const VitaBrosseApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      debugShowCheckedModeBanner: false,
      navigatorKey: NavigationService.navigatorKey,
      home: const SplashScreen(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/dashboard': (context) => const DashboardScreen(),
      },
      onGenerateRoute: RouteGenerator.generateRoute,
    );
  }
}

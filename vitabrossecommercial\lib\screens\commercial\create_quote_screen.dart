import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart' as business;
import '../../providers/quote_provider.dart';
import '../../providers/commercial_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_button.dart';

class CreateQuoteScreen extends ConsumerStatefulWidget {
  const CreateQuoteScreen({super.key});

  @override
  ConsumerState<CreateQuoteScreen> createState() => _CreateQuoteScreenState();
}

class _CreateQuoteScreenState extends ConsumerState<CreateQuoteScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  DateTime? _selectedValidUntil;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quoteCreationState = ref.watch(quoteCreationProvider);
    final clients = ref.watch(commercialClientsProvider);

    // Listen to quote creation state changes
    ref.listen<QuoteCreationState>(quoteCreationProvider, (previous, next) {
      if (next.createdQuote != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Devis créé avec succès!'),
            backgroundColor: Color(AppColors.successColorValue),
          ),
        );
        Navigator.of(context).pop();
      } else if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouveau Devis'),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(quoteCreationProvider.notifier).reset();
            },
            child: const Text('Réinitialiser'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Client Selection
                    Text(
                      'Sélectionner un client',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    clients.when(
                      data: (clientsList) {
                        if (clientsList.isEmpty) {
                          return const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: Text('Aucun client disponible'),
                            ),
                          );
                        }
                        
                        return Card(
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: DropdownButtonFormField<business.Client>(
                              value: quoteCreationState.selectedClient,
                              decoration: const InputDecoration(
                                labelText: 'Client',
                                border: OutlineInputBorder(),
                              ),
                              items: clientsList.map((client) {
                                return DropdownMenuItem<business.Client>(
                                  value: client,
                                  child: Text(client.name),
                                );
                              }).toList(),
                              onChanged: (client) {
                                if (client != null) {
                                  ref.read(quoteCreationProvider.notifier).selectClient(client);
                                }
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Veuillez sélectionner un client';
                                }
                                return null;
                              },
                            ),
                          ),
                        );
                      },
                      loading: () => const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      ),
                      error: (error, stack) => Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text('Erreur: $error'),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Line Items Section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Articles (${quoteCreationState.lineItems.length})',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: () {
                            _showAddProductDialog();
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Ajouter'),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    if (quoteCreationState.lineItems.isEmpty)
                      const Card(
                        child: Padding(
                          padding: EdgeInsets.all(32),
                          child: Center(
                            child: Column(
                              children: [
                                Icon(
                                  Icons.description_outlined,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Aucun article ajouté',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    else
                      ...quoteCreationState.lineItems.asMap().entries.map((entry) {
                        final index = entry.key;
                        final item = entry.value;
                        return _buildLineItemCard(item, index);
                      }),
                    
                    const SizedBox(height: 24),
                    
                    // Quote Summary
                    if (quoteCreationState.lineItems.isNotEmpty) ...[
                      Text(
                        'Résumé du devis',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      
                      const SizedBox(height: 12),
                      
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              _buildSummaryRow('Sous-total', '${quoteCreationState.subtotal.toStringAsFixed(2)}€'),
                              _buildSummaryRow('Remise', '-${quoteCreationState.discountTotal.toStringAsFixed(2)}€'),
                              _buildSummaryRow('TVA (20%)', '${quoteCreationState.taxTotal.toStringAsFixed(2)}€'),
                              const Divider(),
                              _buildSummaryRow(
                                'Total',
                                '${quoteCreationState.totalAmount.toStringAsFixed(2)}€',
                                isTotal: true,
                              ),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                    ],
                    
                    // Additional Information
                    Text(
                      'Informations complémentaires',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Valid Until Date
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: CustomDateField(
                          controller: TextEditingController(
                            text: _selectedValidUntil != null
                                ? '${_selectedValidUntil!.day}/${_selectedValidUntil!.month}/${_selectedValidUntil!.year}'
                                : '',
                          ),
                          label: 'Valable jusqu\'au (optionnel)',
                          selectedDate: _selectedValidUntil,
                          onDateSelected: (date) {
                            setState(() {
                              _selectedValidUntil = date;
                            });
                            ref.read(quoteCreationProvider.notifier).setValidUntil(date);
                          },
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Notes
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: CustomTextField(
                          controller: _notesController,
                          label: 'Notes (optionnelles)',
                          maxLines: 3,
                          hint: 'Conditions particulières, informations complémentaires...',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // Bottom Action Bar
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: LoadingButton(
                  onPressed: quoteCreationState.selectedClient != null && 
                            quoteCreationState.lineItems.isNotEmpty
                      ? _createQuote
                      : null,
                  isLoading: quoteCreationState.isLoading,
                  text: 'Créer le devis',
                  width: double.infinity,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLineItemCard(business.OrderLineItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.productName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Quantité: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)}€',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  if (item.discount > 0)
                    Text(
                      'Remise: -${item.discount.toStringAsFixed(2)}€',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: const Color(AppColors.errorColorValue),
                      ),
                    ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${item.total.toStringAsFixed(2)}€',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(AppColors.primaryColorValue),
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () {
                        // TODO: Edit line item
                      },
                      icon: const Icon(Icons.edit, size: 20),
                      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    ),
                    IconButton(
                      onPressed: () {
                        ref.read(quoteCreationProvider.notifier).removeLineItem(index);
                      },
                      icon: const Icon(Icons.delete, size: 20),
                      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? const Color(AppColors.primaryColorValue) : null,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddProductDialog() {
    // TODO: Implement product selection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sélection de produits à implémenter'),
        backgroundColor: Color(AppColors.infoColorValue),
      ),
    );
  }

  Future<void> _createQuote() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    await ref.read(quoteCreationProvider.notifier).createQuote(
      notes: _notesController.text.trim(),
    );
  }
}

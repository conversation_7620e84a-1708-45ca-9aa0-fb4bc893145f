name: vitabrossecommercial
description: "VitaBrosse Mobile - Application mobile pour commerciaux et merchandiseurs"
publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # Navigation
  go_router: ^14.6.1

  # HTTP and API
  http: ^1.2.2
  dio: ^5.7.0

  # Local Storage
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.4.1

  # Authentication & Security
  local_auth: ^2.3.0
  crypto: ^3.0.5
  encrypt: ^5.0.3

  # Location & Maps
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  google_maps_flutter: ^2.9.0

  # Image & Camera
  image_picker: ^1.1.2
  image: ^4.2.0
  cached_network_image: ^3.4.1

  # PDF Generation
  pdf: ^3.11.1
  printing: ^5.13.2

  # WhatsApp Integration
  url_launcher: ^6.3.1
  share_plus: ^10.0.2

  # Date & Time
  intl: ^0.19.0

  # Utilities
  uuid: ^4.5.1
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  connectivity_plus: ^6.0.5
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  hive_generator: ^2.0.1
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/
    - assets/documents/

  # Fonts
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';

// Location Model
class Location {
  final double latitude;
  final double longitude;
  final String address;

  const Location({
    required this.latitude,
    required this.longitude,
    required this.address,
  });

  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
    };
  }

  factory Location.fromMap(Map<String, dynamic> map) {
    return Location(
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      address: map['address'] ?? '',
    );
  }
}

// Mission Model
class Mission {
  final String id;
  final String merchandizerId;
  final String merchandizerName;
  final String clientId;
  final String clientName;
  final String clientAddress;
  final String title;
  final String description;
  final DateTime missionDate;
  final String startTime;
  final String endTime;
  final int estimatedDuration;
  final MissionStatus status;
  final MissionPriority priority;
  final List<String> tasks;
  final List<String> objectives;
  final Location location;
  final String createdBy;
  final String createdByName;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Mission({
    required this.id,
    required this.merchandizerId,
    required this.merchandizerName,
    required this.clientId,
    required this.clientName,
    required this.clientAddress,
    required this.title,
    required this.description,
    required this.missionDate,
    required this.startTime,
    required this.endTime,
    required this.estimatedDuration,
    required this.status,
    required this.priority,
    required this.tasks,
    required this.objectives,
    required this.location,
    required this.createdBy,
    required this.createdByName,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'merchandizerId': merchandizerId,
      'merchandizerName': merchandizerName,
      'clientId': clientId,
      'clientName': clientName,
      'clientAddress': clientAddress,
      'title': title,
      'description': description,
      'missionDate': Timestamp.fromDate(missionDate),
      'startTime': startTime,
      'endTime': endTime,
      'estimatedDuration': estimatedDuration,
      'status': status.name,
      'priority': priority.name,
      'tasks': tasks,
      'objectives': objectives,
      'location': location.toMap(),
      'createdBy': createdBy,
      'createdByName': createdByName,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Mission.fromMap(Map<String, dynamic> map) {
    return Mission(
      id: map['id'] ?? '',
      merchandizerId: map['merchandizerId'] ?? '',
      merchandizerName: map['merchandizerName'] ?? '',
      clientId: map['clientId'] ?? '',
      clientName: map['clientName'] ?? '',
      clientAddress: map['clientAddress'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      missionDate: (map['missionDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      startTime: map['startTime'] ?? '09:00',
      endTime: map['endTime'] ?? '17:00',
      estimatedDuration: map['estimatedDuration'] ?? AppConstants.defaultEstimatedMissionDuration,
      status: MissionStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => MissionStatus.pending,
      ),
      priority: MissionPriority.values.firstWhere(
        (e) => e.name == map['priority'],
        orElse: () => MissionPriority.medium,
      ),
      tasks: List<String>.from(map['tasks'] ?? []),
      objectives: List<String>.from(map['objectives'] ?? []),
      location: Location.fromMap(map['location'] ?? {}),
      createdBy: map['createdBy'] ?? '',
      createdByName: map['createdByName'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Mission copyWith({
    String? id,
    String? merchandizerId,
    String? merchandizerName,
    String? clientId,
    String? clientName,
    String? clientAddress,
    String? title,
    String? description,
    DateTime? missionDate,
    String? startTime,
    String? endTime,
    int? estimatedDuration,
    MissionStatus? status,
    MissionPriority? priority,
    List<String>? tasks,
    List<String>? objectives,
    Location? location,
    String? createdBy,
    String? createdByName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Mission(
      id: id ?? this.id,
      merchandizerId: merchandizerId ?? this.merchandizerId,
      merchandizerName: merchandizerName ?? this.merchandizerName,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      clientAddress: clientAddress ?? this.clientAddress,
      title: title ?? this.title,
      description: description ?? this.description,
      missionDate: missionDate ?? this.missionDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      tasks: tasks ?? this.tasks,
      objectives: objectives ?? this.objectives,
      location: location ?? this.location,
      createdBy: createdBy ?? this.createdBy,
      createdByName: createdByName ?? this.createdByName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum MissionStatus { pending, inProgress, completed, cancelled }
enum MissionPriority { low, medium, high, urgent }

// Mission Photo Model
class MissionPhoto {
  final String id;
  final String url;
  final String caption;
  final PhotoCategory category;
  final DateTime timestamp;

  const MissionPhoto({
    required this.id,
    required this.url,
    required this.caption,
    required this.category,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'url': url,
      'caption': caption,
      'category': category.name,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }

  factory MissionPhoto.fromMap(Map<String, dynamic> map) {
    return MissionPhoto(
      id: map['id'] ?? '',
      url: map['url'] ?? '',
      caption: map['caption'] ?? '',
      category: PhotoCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => PhotoCategory.display,
      ),
      timestamp: (map['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

enum PhotoCategory { before, after, issue, solution, display }

// Mission Report Model
class MissionReport {
  final String id;
  final String missionId;
  final String merchandizerId;
  final String actualStartTime;
  final String actualEndTime;
  final int duration;
  final List<String> completedTasks;
  final List<String> achievedObjectives;
  final double completionRate;
  final int storeRating;
  final int staffCooperation;
  final int displayQuality;
  final List<MissionPhoto> photos;
  final List<String> issues;
  final List<String> solutions;
  final List<String> recommendations;
  final String notes;
  final bool followUpRequired;
  final String? followUpNotes;
  final ReportStatus status;
  final DateTime submittedDate;
  final String? reviewedBy;
  final DateTime? reviewedDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MissionReport({
    required this.id,
    required this.missionId,
    required this.merchandizerId,
    required this.actualStartTime,
    required this.actualEndTime,
    required this.duration,
    required this.completedTasks,
    required this.achievedObjectives,
    required this.completionRate,
    required this.storeRating,
    required this.staffCooperation,
    required this.displayQuality,
    required this.photos,
    required this.issues,
    required this.solutions,
    required this.recommendations,
    required this.notes,
    required this.followUpRequired,
    this.followUpNotes,
    required this.status,
    required this.submittedDate,
    this.reviewedBy,
    this.reviewedDate,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'missionId': missionId,
      'merchandizerId': merchandizerId,
      'actualStartTime': actualStartTime,
      'actualEndTime': actualEndTime,
      'duration': duration,
      'completedTasks': completedTasks,
      'achievedObjectives': achievedObjectives,
      'completionRate': completionRate,
      'storeRating': storeRating,
      'staffCooperation': staffCooperation,
      'displayQuality': displayQuality,
      'photos': photos.map((photo) => photo.toMap()).toList(),
      'issues': issues,
      'solutions': solutions,
      'recommendations': recommendations,
      'notes': notes,
      'followUpRequired': followUpRequired,
      'followUpNotes': followUpNotes,
      'status': status.name,
      'submittedDate': Timestamp.fromDate(submittedDate),
      'reviewedBy': reviewedBy,
      'reviewedDate': reviewedDate != null ? Timestamp.fromDate(reviewedDate!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory MissionReport.fromMap(Map<String, dynamic> map) {
    return MissionReport(
      id: map['id'] ?? '',
      missionId: map['missionId'] ?? '',
      merchandizerId: map['merchandizerId'] ?? '',
      actualStartTime: map['actualStartTime'] ?? '',
      actualEndTime: map['actualEndTime'] ?? '',
      duration: map['duration'] ?? 0,
      completedTasks: List<String>.from(map['completedTasks'] ?? []),
      achievedObjectives: List<String>.from(map['achievedObjectives'] ?? []),
      completionRate: map['completionRate']?.toDouble() ?? 0.0,
      storeRating: map['storeRating'] ?? 0,
      staffCooperation: map['staffCooperation'] ?? 0,
      displayQuality: map['displayQuality'] ?? 0,
      photos: (map['photos'] as List<dynamic>?)
          ?.map((photo) => MissionPhoto.fromMap(photo))
          .toList() ?? [],
      issues: List<String>.from(map['issues'] ?? []),
      solutions: List<String>.from(map['solutions'] ?? []),
      recommendations: List<String>.from(map['recommendations'] ?? []),
      notes: map['notes'] ?? '',
      followUpRequired: map['followUpRequired'] ?? false,
      followUpNotes: map['followUpNotes'],
      status: ReportStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ReportStatus.draft,
      ),
      submittedDate: (map['submittedDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      reviewedBy: map['reviewedBy'],
      reviewedDate: (map['reviewedDate'] as Timestamp?)?.toDate(),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  MissionReport copyWith({
    String? id,
    String? missionId,
    String? merchandizerId,
    String? actualStartTime,
    String? actualEndTime,
    int? duration,
    List<String>? completedTasks,
    List<String>? achievedObjectives,
    double? completionRate,
    int? storeRating,
    int? staffCooperation,
    int? displayQuality,
    List<MissionPhoto>? photos,
    List<String>? issues,
    List<String>? solutions,
    List<String>? recommendations,
    String? notes,
    bool? followUpRequired,
    String? followUpNotes,
    ReportStatus? status,
    DateTime? submittedDate,
    String? reviewedBy,
    DateTime? reviewedDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MissionReport(
      id: id ?? this.id,
      missionId: missionId ?? this.missionId,
      merchandizerId: merchandizerId ?? this.merchandizerId,
      actualStartTime: actualStartTime ?? this.actualStartTime,
      actualEndTime: actualEndTime ?? this.actualEndTime,
      duration: duration ?? this.duration,
      completedTasks: completedTasks ?? this.completedTasks,
      achievedObjectives: achievedObjectives ?? this.achievedObjectives,
      completionRate: completionRate ?? this.completionRate,
      storeRating: storeRating ?? this.storeRating,
      staffCooperation: staffCooperation ?? this.staffCooperation,
      displayQuality: displayQuality ?? this.displayQuality,
      photos: photos ?? this.photos,
      issues: issues ?? this.issues,
      solutions: solutions ?? this.solutions,
      recommendations: recommendations ?? this.recommendations,
      notes: notes ?? this.notes,
      followUpRequired: followUpRequired ?? this.followUpRequired,
      followUpNotes: followUpNotes ?? this.followUpNotes,
      status: status ?? this.status,
      submittedDate: submittedDate ?? this.submittedDate,
      reviewedBy: reviewedBy ?? this.reviewedBy,
      reviewedDate: reviewedDate ?? this.reviewedDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum ReportStatus { draft, submitted, approved, rejected }

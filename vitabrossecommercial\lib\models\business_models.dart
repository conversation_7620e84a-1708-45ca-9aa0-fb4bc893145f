import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';

// Client Model
class Client {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String address;
  final String city;
  final String postalCode;
  final String country;
  final String territory;
  final List<String> assignedCommercials;
  final ClientStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Client({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.address,
    required this.city,
    required this.postalCode,
    required this.country,
    required this.territory,
    required this.assignedCommercials,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'city': city,
      'postalCode': postalCode,
      'country': country,
      'territory': territory,
      'assignedCommercials': assignedCommercials,
      'status': status.name,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      postalCode: map['postalCode'] ?? '',
      country: map['country'] ?? '',
      territory: map['territory'] ?? '',
      assignedCommercials: List<String>.from(map['assignedCommercials'] ?? []),
      status: ClientStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ClientStatus.active,
      ),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Client copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? postalCode,
    String? country,
    String? territory,
    List<String>? assignedCommercials,
    ClientStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      city: city ?? this.city,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      territory: territory ?? this.territory,
      assignedCommercials: assignedCommercials ?? this.assignedCommercials,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum ClientStatus { active, inactive, suspended }

// Product Model
class Product {
  final String id;
  final String name;
  final String description;
  final String category;
  final double price;
  final String currency;
  final int stockQuantity;
  final String unit;
  final List<String> imageUrls;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Product({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.price,
    required this.currency,
    required this.stockQuantity,
    required this.unit,
    required this.imageUrls,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'price': price,
      'currency': currency,
      'stockQuantity': stockQuantity,
      'unit': unit,
      'imageUrls': imageUrls,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? '',
      price: map['price']?.toDouble() ?? 0.0,
      currency: map['currency'] ?? 'EUR',
      stockQuantity: map['stockQuantity'] ?? 0,
      unit: map['unit'] ?? 'pcs',
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Product copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    double? price,
    String? currency,
    int? stockQuantity,
    String? unit,
    List<String>? imageUrls,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      unit: unit ?? this.unit,
      imageUrls: imageUrls ?? this.imageUrls,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Order Line Item Model
class OrderLineItem {
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double discount;
  final double total;

  const OrderLineItem({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.discount,
    required this.total,
  });

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'discount': discount,
      'total': total,
    };
  }

  factory OrderLineItem.fromMap(Map<String, dynamic> map) {
    return OrderLineItem(
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      quantity: map['quantity'] ?? 0,
      unitPrice: map['unitPrice']?.toDouble() ?? 0.0,
      discount: map['discount']?.toDouble() ?? 0.0,
      total: map['total']?.toDouble() ?? 0.0,
    );
  }
}

// Order Model
class Order {
  final String id;
  final String orderNumber;
  final String clientId;
  final String clientName;
  final String commercialId;
  final String commercialName;
  final List<OrderLineItem> lineItems;
  final double subtotal;
  final double discountTotal;
  final double taxTotal;
  final double totalAmount;
  final OrderStatus status;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final String notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Order({
    required this.id,
    required this.orderNumber,
    required this.clientId,
    required this.clientName,
    required this.commercialId,
    required this.commercialName,
    required this.lineItems,
    required this.subtotal,
    required this.discountTotal,
    required this.taxTotal,
    required this.totalAmount,
    required this.status,
    required this.orderDate,
    this.deliveryDate,
    required this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'orderNumber': orderNumber,
      'clientId': clientId,
      'clientName': clientName,
      'commercialId': commercialId,
      'commercialName': commercialName,
      'lineItems': lineItems.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'discountTotal': discountTotal,
      'taxTotal': taxTotal,
      'totalAmount': totalAmount,
      'status': status.name,
      'orderDate': Timestamp.fromDate(orderDate),
      'deliveryDate':
          deliveryDate != null ? Timestamp.fromDate(deliveryDate!) : null,
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'] ?? '',
      orderNumber: map['orderNumber'] ?? '',
      clientId: map['clientId'] ?? '',
      clientName: map['clientName'] ?? '',
      commercialId: map['commercialId'] ?? '',
      commercialName: map['commercialName'] ?? '',
      lineItems: (map['lineItems'] as List<dynamic>?)
              ?.map((item) => OrderLineItem.fromMap(item))
              .toList() ??
          [],
      subtotal: map['subtotal']?.toDouble() ?? 0.0,
      discountTotal: map['discountTotal']?.toDouble() ?? 0.0,
      taxTotal: map['taxTotal']?.toDouble() ?? 0.0,
      totalAmount: map['totalAmount']?.toDouble() ?? 0.0,
      status: OrderStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => OrderStatus.pending,
      ),
      orderDate: (map['orderDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deliveryDate: (map['deliveryDate'] as Timestamp?)?.toDate(),
      notes: map['notes'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

enum OrderStatus {
  pending,
  confirmed,
  processing,
  shipped,
  delivered,
  cancelled
}

// Quote Model
class Quote {
  final String id;
  final String quoteNumber;
  final String clientId;
  final String clientName;
  final String clientEmail;
  final String clientPhone;
  final String commercialId;
  final String commercialName;
  final List<OrderLineItem> lineItems;
  final double subtotal;
  final double discountTotal;
  final double taxTotal;
  final double totalAmount;
  final DateTime quoteDate;
  final DateTime validUntil;
  final String notes;
  final bool whatsappSent;
  final DateTime? whatsappSentDate;
  final String clientWhatsapp;
  final QuoteStatus status;
  final bool convertedToOrder;
  final String? orderId;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Quote({
    required this.id,
    required this.quoteNumber,
    required this.clientId,
    required this.clientName,
    required this.clientEmail,
    required this.clientPhone,
    required this.commercialId,
    required this.commercialName,
    required this.lineItems,
    required this.subtotal,
    required this.discountTotal,
    required this.taxTotal,
    required this.totalAmount,
    required this.quoteDate,
    required this.validUntil,
    required this.notes,
    required this.whatsappSent,
    this.whatsappSentDate,
    required this.clientWhatsapp,
    required this.status,
    required this.convertedToOrder,
    this.orderId,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quoteNumber': quoteNumber,
      'clientId': clientId,
      'clientName': clientName,
      'clientEmail': clientEmail,
      'clientPhone': clientPhone,
      'commercialId': commercialId,
      'commercialName': commercialName,
      'lineItems': lineItems.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'discountTotal': discountTotal,
      'taxTotal': taxTotal,
      'totalAmount': totalAmount,
      'quoteDate': Timestamp.fromDate(quoteDate),
      'validUntil': Timestamp.fromDate(validUntil),
      'notes': notes,
      'whatsappSent': whatsappSent,
      'whatsappSentDate': whatsappSentDate != null
          ? Timestamp.fromDate(whatsappSentDate!)
          : null,
      'clientWhatsapp': clientWhatsapp,
      'status': status.name,
      'convertedToOrder': convertedToOrder,
      'orderId': orderId,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Quote.fromMap(Map<String, dynamic> map) {
    return Quote(
      id: map['id'] ?? '',
      quoteNumber: map['quoteNumber'] ?? '',
      clientId: map['clientId'] ?? '',
      clientName: map['clientName'] ?? '',
      clientEmail: map['clientEmail'] ?? '',
      clientPhone: map['clientPhone'] ?? '',
      commercialId: map['commercialId'] ?? '',
      commercialName: map['commercialName'] ?? '',
      lineItems: (map['lineItems'] as List<dynamic>?)
              ?.map((item) => OrderLineItem.fromMap(item))
              .toList() ??
          [],
      subtotal: map['subtotal']?.toDouble() ?? 0.0,
      discountTotal: map['discountTotal']?.toDouble() ?? 0.0,
      taxTotal: map['taxTotal']?.toDouble() ?? 0.0,
      totalAmount: map['totalAmount']?.toDouble() ?? 0.0,
      quoteDate: (map['quoteDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      validUntil: (map['validUntil'] as Timestamp?)?.toDate() ??
          DateTime.now().add(const Duration(days: 30)),
      notes: map['notes'] ?? '',
      whatsappSent: map['whatsappSent'] ?? false,
      whatsappSentDate: (map['whatsappSentDate'] as Timestamp?)?.toDate(),
      clientWhatsapp: map['clientWhatsapp'] ?? '',
      status: QuoteStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => QuoteStatus.pending,
      ),
      convertedToOrder: map['convertedToOrder'] ?? false,
      orderId: map['orderId'],
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

enum QuoteStatus { pending, sent, viewed, accepted, rejected, expired }

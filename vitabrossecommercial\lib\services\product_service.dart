import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/business_models.dart' as business;
import '../services/firebase/firebase_service.dart';

class ProductService {
  // Create a new product
  Future<business.Product> createProduct({
    required String name,
    required String description,
    required String category,
    required String sku,
    required double price,
    required int stockQuantity,
    required int minStockLevel,
    String? imageUrl,
    Map<String, dynamic>? specifications,
  }) async {
    // Check if SKU already exists
    final existingProduct = await getProductBySku(sku);
    if (existingProduct != null) {
      throw Exception('Un produit avec ce SKU existe déjà');
    }

    final product = business.Product(
      id: FirebaseService.generateId(),
      name: name,
      description: description,
      category: category,
      sku: sku,
      price: price,
      stockQuantity: stockQuantity,
      minStockLevel: minStockLevel,
      imageUrl: imageUrl ?? '',
      specifications: specifications ?? {},
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Save to Firestore
    await FirebaseService.setDocument(
      FirebaseService.productsCollection.doc(product.id),
      product.toMap(),
    );

    return product;
  }

  // Update product
  Future<void> updateProduct(String productId, Map<String, dynamic> updates) async {
    final updateData = {
      ...updates,
      'updatedAt': FieldValue.serverTimestamp(),
    };

    await FirebaseService.updateDocument(
      FirebaseService.productsCollection.doc(productId),
      updateData,
    );
  }

  // Update stock quantity
  Future<void> updateStockQuantity(String productId, int newQuantity) async {
    await updateProduct(productId, {
      'stockQuantity': newQuantity,
    });
  }

  // Adjust stock (add or remove)
  Future<void> adjustStock(String productId, int adjustment, String reason) async {
    final productDoc = await FirebaseService.getDocument(
      FirebaseService.productsCollection.doc(productId),
    );

    if (!productDoc.exists) {
      throw Exception('Produit non trouvé');
    }

    final product = business.Product.fromMap(productDoc.data() as Map<String, dynamic>);
    final newQuantity = product.stockQuantity + adjustment;

    if (newQuantity < 0) {
      throw Exception('La quantité en stock ne peut pas être négative');
    }

    // Update stock quantity
    await updateStockQuantity(productId, newQuantity);

    // Log stock movement
    await _logStockMovement(
      productId: productId,
      productName: product.name,
      movementType: adjustment > 0 ? StockMovementType.stockIn : StockMovementType.stockOut,
      quantity: adjustment.abs(),
      reason: reason,
      previousQuantity: product.stockQuantity,
      newQuantity: newQuantity,
    );
  }

  // Delete product (soft delete)
  Future<void> deleteProduct(String productId) async {
    await updateProduct(productId, {
      'isActive': false,
    });
  }

  // Restore product
  Future<void> restoreProduct(String productId) async {
    await updateProduct(productId, {
      'isActive': true,
    });
  }

  // Get product by ID
  Future<business.Product?> getProductById(String productId) async {
    final doc = await FirebaseService.getDocument(
      FirebaseService.productsCollection.doc(productId),
    );

    if (doc.exists) {
      return business.Product.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }

  // Get product by SKU
  Future<business.Product?> getProductBySku(String sku) async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.productsCollection,
      queryBuilder: (query) => query.where('sku', isEqualTo: sku).limit(1),
    );

    if (snapshot.docs.isNotEmpty) {
      return business.Product.fromMap(snapshot.docs.first.data() as Map<String, dynamic>);
    }
    return null;
  }

  // Get products by category
  Future<List<business.Product>> getProductsByCategory(String category) async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.productsCollection,
      queryBuilder: (query) => query
          .where('category', isEqualTo: category)
          .where('isActive', isEqualTo: true)
          .orderBy('name'),
    );

    return snapshot.docs
        .map((doc) => business.Product.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Get low stock products
  Future<List<business.Product>> getLowStockProducts() async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.productsCollection,
      queryBuilder: (query) => query
          .where('isActive', isEqualTo: true)
          .orderBy('stockQuantity'),
    );

    final products = snapshot.docs
        .map((doc) => business.Product.fromMap(doc.data() as Map<String, dynamic>))
        .toList();

    // Filter products where stock is at or below minimum level
    return products.where((product) => 
        product.stockQuantity <= product.minStockLevel).toList();
  }

  // Search products
  Future<List<business.Product>> searchProducts(String query) async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.productsCollection,
      queryBuilder: (query) => query
          .where('isActive', isEqualTo: true)
          .orderBy('name'),
    );

    final products = snapshot.docs
        .map((doc) => business.Product.fromMap(doc.data() as Map<String, dynamic>))
        .toList();

    final searchQuery = query.toLowerCase();
    return products.where((product) =>
        product.name.toLowerCase().contains(searchQuery) ||
        product.description.toLowerCase().contains(searchQuery) ||
        product.category.toLowerCase().contains(searchQuery) ||
        product.sku.toLowerCase().contains(searchQuery)
    ).toList();
  }

  // Get stock movements for a product
  Future<List<StockMovement>> getStockMovements(String productId) async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.stockMovementsCollection,
      queryBuilder: (query) => query
          .where('productId', isEqualTo: productId)
          .orderBy('createdAt', descending: true)
          .limit(50),
    );

    return snapshot.docs
        .map((doc) => StockMovement.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Private helper methods
  Future<void> _logStockMovement({
    required String productId,
    required String productName,
    required StockMovementType movementType,
    required int quantity,
    required String reason,
    required int previousQuantity,
    required int newQuantity,
  }) async {
    final movement = StockMovement(
      id: FirebaseService.generateId(),
      productId: productId,
      productName: productName,
      movementType: movementType,
      quantity: quantity,
      reason: reason,
      previousQuantity: previousQuantity,
      newQuantity: newQuantity,
      createdAt: DateTime.now(),
    );

    await FirebaseService.setDocument(
      FirebaseService.stockMovementsCollection.doc(movement.id),
      movement.toMap(),
    );
  }
}

// Stock movement model
class StockMovement {
  final String id;
  final String productId;
  final String productName;
  final StockMovementType movementType;
  final int quantity;
  final String reason;
  final int previousQuantity;
  final int newQuantity;
  final DateTime createdAt;

  const StockMovement({
    required this.id,
    required this.productId,
    required this.productName,
    required this.movementType,
    required this.quantity,
    required this.reason,
    required this.previousQuantity,
    required this.newQuantity,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'movementType': movementType.name,
      'quantity': quantity,
      'reason': reason,
      'previousQuantity': previousQuantity,
      'newQuantity': newQuantity,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }

  factory StockMovement.fromMap(Map<String, dynamic> map) {
    return StockMovement(
      id: map['id'] ?? '',
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      movementType: StockMovementType.values.firstWhere(
        (e) => e.name == map['movementType'],
        orElse: () => StockMovementType.adjustment,
      ),
      quantity: map['quantity']?.toInt() ?? 0,
      reason: map['reason'] ?? '',
      previousQuantity: map['previousQuantity']?.toInt() ?? 0,
      newQuantity: map['newQuantity']?.toInt() ?? 0,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

enum StockMovementType {
  stockIn,
  stockOut,
  adjustment,
  sale,
  return_,
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../providers/commercial_provider.dart';
import '../../models/business_models.dart';
import '../../services/navigation_service.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/cards/performance_card.dart';

class ClientsScreen extends ConsumerStatefulWidget {
  const ClientsScreen({super.key});

  @override
  ConsumerState<ClientsScreen> createState() => _ClientsScreenState();
}

class _ClientsScreenState extends ConsumerState<ClientsScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  ClientStatus? _selectedStatus;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final clientsAsync = ref.watch(commercialClientsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes Clients'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // TODO: Navigate to add client
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              children: [
                // Search Bar
                CustomSearchField(
                  controller: _searchController,
                  hint: 'Rechercher un client...',
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),

                const SizedBox(height: 12),

                // Status Filter
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'Tous',
                        _selectedStatus == null,
                        () => setState(() => _selectedStatus = null),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Actifs',
                        _selectedStatus == ClientStatus.active,
                        () => setState(
                            () => _selectedStatus = ClientStatus.active),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Inactifs',
                        _selectedStatus == ClientStatus.inactive,
                        () => setState(
                            () => _selectedStatus = ClientStatus.inactive),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Suspendus',
                        _selectedStatus == ClientStatus.suspended,
                        () => setState(
                            () => _selectedStatus = ClientStatus.suspended),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Clients List
          Expanded(
            child: clientsAsync.when(
              data: (clients) {
                final filteredClients = _filterClients(clients);

                if (filteredClients.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(commercialClientsProvider);
                  },
                  child: Column(
                    children: [
                      // Stats Summary
                      Container(
                        padding:
                            const EdgeInsets.all(AppConstants.defaultPadding),
                        child: Row(
                          children: [
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Total',
                                value: clients.length.toString(),
                                icon: Icons.people,
                                color: const Color(AppColors.primaryColorValue),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Actifs',
                                value: clients
                                    .where(
                                        (c) => c.status == ClientStatus.active)
                                    .length
                                    .toString(),
                                icon: Icons.check_circle,
                                color: const Color(AppColors.successColorValue),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Résultats',
                                value: filteredClients.length.toString(),
                                icon: Icons.search,
                                color: const Color(AppColors.infoColorValue),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Clients List
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                          ),
                          itemCount: filteredClients.length,
                          itemBuilder: (context, index) {
                            final client = filteredClients[index];
                            return _buildClientCard(context, client);
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Color(AppColors.errorColorValue),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Erreur de chargement',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.invalidate(commercialClientsProvider);
                      },
                      child: const Text('Réessayer'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Client> _filterClients(List<Client> clients) {
    return clients.where((client) {
      final matchesSearch = _searchQuery.isEmpty ||
          client.name.toLowerCase().contains(_searchQuery) ||
          client.email.toLowerCase().contains(_searchQuery) ||
          client.city.toLowerCase().contains(_searchQuery);

      final matchesStatus =
          _selectedStatus == null || client.status == _selectedStatus;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor:
          const Color(AppColors.primaryColorValue).withValues(alpha: 0.2),
      checkmarkColor: const Color(AppColors.primaryColorValue),
    );
  }

  Widget _buildClientCard(BuildContext context, Client client) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to client details
        },
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getStatusColor(client.status),
                    child: Text(
                      client.name.isNotEmpty
                          ? client.name[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          client.name,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          client.email,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: const Color(
                                        AppColors.secondaryTextColorValue),
                                  ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color:
                          _getStatusColor(client.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(client.status),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: _getStatusColor(client.status),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${client.city}, ${client.country}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                const Color(AppColors.secondaryTextColorValue),
                          ),
                    ),
                  ),
                  Icon(
                    Icons.phone,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    client.phone,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildActionButton(
                    context,
                    'Commande',
                    Icons.shopping_cart,
                    () {
                      // TODO: Create order for client
                    },
                  ),
                  _buildActionButton(
                    context,
                    'Devis',
                    Icons.description,
                    () {
                      // TODO: Create quote for client
                    },
                  ),
                  _buildActionButton(
                    context,
                    'Appeler',
                    Icons.phone,
                    () {
                      // TODO: Call client
                    },
                  ),
                  _buildActionButton(
                    context,
                    'Email',
                    Icons.email,
                    () {
                      // TODO: Email client
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 20,
              color: const Color(AppColors.primaryColorValue),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(AppColors.primaryColorValue),
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun client trouvé',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: const Color(AppColors.secondaryTextColorValue),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Essayez de modifier vos critères de recherche',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: const Color(AppColors.secondaryTextColorValue),
                ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(ClientStatus status) {
    switch (status) {
      case ClientStatus.active:
        return const Color(AppColors.successColorValue);
      case ClientStatus.inactive:
        return const Color(AppColors.warningColorValue);
      case ClientStatus.suspended:
        return const Color(AppColors.errorColorValue);
    }
  }

  String _getStatusText(ClientStatus status) {
    switch (status) {
      case ClientStatus.active:
        return 'Actif';
      case ClientStatus.inactive:
        return 'Inactif';
      case ClientStatus.suspended:
        return 'Suspendu';
    }
  }
}

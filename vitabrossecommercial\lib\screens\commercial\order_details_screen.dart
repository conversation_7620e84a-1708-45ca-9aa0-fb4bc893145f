import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart' as business;
import '../../providers/order_provider.dart';
import '../../widgets/common/loading_button.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  final String orderId;

  const OrderDetailsScreen({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  bool _isUpdatingStatus = false;

  @override
  Widget build(BuildContext context) {
    final orderAsync = ref.watch(orderProvider(widget.orderId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails de la commande'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: Share order
            },
          ),
        ],
      ),
      body: orderAsync.when(
        data: (order) {
          if (order == null) {
            return const Center(
              child: Text('Commande non trouvée'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Order Header
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              order.orderNumber,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(order.status)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                _getStatusText(order.status),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: _getStatusColor(order.status),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Icon(
                              Icons.person,
                              size: 20,
                              color: const Color(
                                  AppColors.secondaryTextColorValue),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Client: ${order.clientName}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 20,
                              color: const Color(
                                  AppColors.secondaryTextColorValue),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Commande: ${_formatDate(order.orderDate)}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        if (order.deliveryDate != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.local_shipping,
                                size: 20,
                                color: const Color(
                                    AppColors.secondaryTextColorValue),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Livraison: ${_formatDate(order.deliveryDate!)}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Line Items
                Text(
                  'Articles commandés',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),

                const SizedBox(height: 12),

                Card(
                  child: Column(
                    children: [
                      ...order.lineItems
                          .map((item) => _buildLineItemTile(item)),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Order Summary
                Text(
                  'Résumé',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),

                const SizedBox(height: 12),

                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      children: [
                        _buildSummaryRow('Sous-total',
                            '${order.subtotal.toStringAsFixed(2)}€'),
                        _buildSummaryRow('Remise',
                            '-${order.discountTotal.toStringAsFixed(2)}€'),
                        _buildSummaryRow(
                            'TVA', '${order.taxTotal.toStringAsFixed(2)}€'),
                        const Divider(),
                        _buildSummaryRow(
                          'Total',
                          '${order.totalAmount.toStringAsFixed(2)}€',
                          isTotal: true,
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Notes
                if (order.notes.isNotEmpty) ...[
                  Text(
                    'Notes',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 12),
                  Card(
                    child: Padding(
                      padding:
                          const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Text(
                        order.notes,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Status Actions
                if (order.status == OrderStatus.pending) ...[
                  Text(
                    'Actions',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: LoadingButton(
                          onPressed: () =>
                              _updateOrderStatus(order, OrderStatus.confirmed),
                          isLoading: _isUpdatingStatus,
                          text: 'Confirmer',
                          backgroundColor:
                              const Color(AppColors.successColorValue),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: LoadingButton(
                          onPressed: () =>
                              _updateOrderStatus(order, OrderStatus.cancelled),
                          isLoading: _isUpdatingStatus,
                          text: 'Annuler',
                          backgroundColor:
                              const Color(AppColors.errorColorValue),
                          isOutlined: true,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 32),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Color(AppColors.errorColorValue),
              ),
              const SizedBox(height: 16),
              Text(
                'Erreur de chargement',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLineItemTile(business.OrderLineItem item) {
    return ListTile(
      title: Text(
        item.productName,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Quantité: ${item.quantity}'),
          Text('Prix unitaire: ${item.unitPrice.toStringAsFixed(2)}€'),
          if (item.discount > 0)
            Text(
              'Remise: -${item.discount.toStringAsFixed(2)}€',
              style: const TextStyle(
                color: Color(AppColors.errorColorValue),
              ),
            ),
        ],
      ),
      trailing: Text(
        '${item.total.toStringAsFixed(2)}€',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
          color: Color(AppColors.primaryColorValue),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                  color:
                      isTotal ? const Color(AppColors.primaryColorValue) : null,
                ),
          ),
        ],
      ),
    );
  }

  Future<void> _updateOrderStatus(
      business.Order order, business.OrderStatus newStatus) async {
    setState(() {
      _isUpdatingStatus = true;
    });

    try {
      final orderService = ref.read(orderServiceProvider);
      await orderService.updateOrderStatus(order.id, newStatus);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Statut mis à jour: ${_getStatusText(newStatus)}'),
            backgroundColor: const Color(AppColors.successColorValue),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingStatus = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getStatusColor(business.OrderStatus status) {
    switch (status) {
      case business.OrderStatus.pending:
        return const Color(AppColors.warningColorValue);
      case business.OrderStatus.confirmed:
        return const Color(AppColors.infoColorValue);
      case business.OrderStatus.processing:
        return const Color(AppColors.primaryColorValue);
      case business.OrderStatus.shipped:
        return Colors.purple;
      case business.OrderStatus.delivered:
        return const Color(AppColors.successColorValue);
      case business.OrderStatus.cancelled:
        return const Color(AppColors.errorColorValue);
    }
  }

  String _getStatusText(business.OrderStatus status) {
    switch (status) {
      case business.OrderStatus.pending:
        return 'En attente';
      case business.OrderStatus.confirmed:
        return 'Confirmée';
      case business.OrderStatus.processing:
        return 'En cours';
      case business.OrderStatus.shipped:
        return 'Expédiée';
      case business.OrderStatus.delivered:
        return 'Livrée';
      case business.OrderStatus.cancelled:
        return 'Annulée';
    }
  }
}

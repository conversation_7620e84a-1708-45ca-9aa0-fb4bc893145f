import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_button.dart';

class ChangePasswordScreen extends ConsumerStatefulWidget {
  final bool isRequired;

  const ChangePasswordScreen({
    super.key,
    this.isRequired = false,
  });

  @override
  ConsumerState<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends ConsumerState<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleChangePassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    await ref.read(authControllerProvider.notifier).changePassword(
      currentPassword: _currentPasswordController.text,
      newPassword: _newPasswordController.text,
    );
  }

  String? _validateCurrentPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.requiredField;
    }
    return null;
  }

  String? _validateNewPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.requiredField;
    }
    if (value.length < AppConstants.minPasswordLength) {
      return AppStrings.passwordTooShort;
    }
    if (value == _currentPasswordController.text) {
      return 'Le nouveau mot de passe doit être différent de l\'ancien';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.requiredField;
    }
    if (value != _newPasswordController.text) {
      return AppStrings.passwordsDoNotMatch;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authControllerProvider);
    final isLoading = authState is AuthLoading;

    // Listen to auth state changes
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      if (next is AuthAuthenticated) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Mot de passe changé avec succès!'),
            backgroundColor: Color(AppColors.successColorValue),
          ),
        );
        
        if (widget.isRequired) {
          // Navigate to main app
          Navigator.of(context).pushReplacementNamed('/dashboard');
        } else {
          Navigator.of(context).pop();
        }
      } else if (next is AuthError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    });

    return Scaffold(
      backgroundColor: const Color(AppColors.backgroundColorValue),
      appBar: widget.isRequired ? null : AppBar(
        title: const Text(AppStrings.changePassword),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.largePadding),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (widget.isRequired) ...[
                  const SizedBox(height: 40),
                  
                  // Header for required password change
                  Center(
                    child: Column(
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: const Color(AppColors.warningColorValue),
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: const Icon(
                            Icons.security,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                        
                        const SizedBox(height: 20),
                        
                        const Text(
                          'Changement de mot de passe requis',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(AppColors.primaryTextColorValue),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 8),
                        
                        const Text(
                          'Pour des raisons de sécurité, vous devez changer votre mot de passe temporaire.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(AppColors.secondaryTextColorValue),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                ],
                
                // Current Password Field
                CustomTextField(
                  controller: _currentPasswordController,
                  label: 'Mot de passe actuel',
                  prefixIcon: Icons.lock_outline,
                  obscureText: _obscureCurrentPassword,
                  validator: _validateCurrentPassword,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureCurrentPassword ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureCurrentPassword = !_obscureCurrentPassword;
                      });
                    },
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // New Password Field
                CustomTextField(
                  controller: _newPasswordController,
                  label: AppStrings.newPassword,
                  prefixIcon: Icons.lock,
                  obscureText: _obscureNewPassword,
                  validator: _validateNewPassword,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureNewPassword ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureNewPassword = !_obscureNewPassword;
                      });
                    },
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Confirm Password Field
                CustomTextField(
                  controller: _confirmPasswordController,
                  label: AppStrings.confirmPassword,
                  prefixIcon: Icons.lock_check,
                  obscureText: _obscureConfirmPassword,
                  validator: _validateConfirmPassword,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Password Requirements
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Exigences du mot de passe:',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Color(AppColors.infoColorValue),
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildPasswordRequirement(
                        'Au moins ${AppConstants.minPasswordLength} caractères',
                        _newPasswordController.text.length >= AppConstants.minPasswordLength,
                      ),
                      _buildPasswordRequirement(
                        'Différent du mot de passe actuel',
                        _newPasswordController.text.isNotEmpty && 
                        _newPasswordController.text != _currentPasswordController.text,
                      ),
                      _buildPasswordRequirement(
                        'Confirmation identique',
                        _confirmPasswordController.text.isNotEmpty && 
                        _confirmPasswordController.text == _newPasswordController.text,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 30),
                
                // Change Password Button
                LoadingButton(
                  onPressed: _handleChangePassword,
                  isLoading: isLoading,
                  text: AppStrings.changePassword,
                ),
                
                if (!widget.isRequired) ...[
                  const SizedBox(height: 16),
                  
                  // Cancel Button
                  LoadingButton(
                    onPressed: () => Navigator.of(context).pop(),
                    text: AppStrings.cancel,
                    isOutlined: true,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordRequirement(String text, bool isValid) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.radio_button_unchecked,
            size: 16,
            color: isValid 
                ? const Color(AppColors.successColorValue)
                : const Color(AppColors.secondaryTextColorValue),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 12,
                color: isValid 
                    ? const Color(AppColors.successColorValue)
                    : const Color(AppColors.secondaryTextColorValue),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

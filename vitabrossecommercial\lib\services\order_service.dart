import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/business_models.dart';
import '../models/user_models.dart';
import '../services/firebase/firebase_service.dart';
import '../services/firebase/auth_service.dart';
import '../constants/app_constants.dart';

class OrderService {
  // Create a new order
  Future<Order> createOrder({
    required Client client,
    required List<OrderLineItem> lineItems,
    required String notes,
    DateTime? deliveryDate,
  }) async {
    final currentUserData = await AuthService.getCurrentUserData();
    if (currentUserData == null || currentUserData['role'] != AppConstants.commercialRole) {
      throw Exception('Seuls les commerciaux peuvent créer des commandes');
    }

    final commercial = currentUserData['data'] as Commercial;
    
    // Validate stock availability
    await _validateStockAvailability(lineItems);
    
    // Calculate totals
    final subtotal = lineItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.unitPrice * item.quantity),
    );
    
    final discountTotal = lineItems.fold<double>(
      0.0,
      (sum, item) => sum + item.discount,
    );
    
    final taxTotal = (subtotal - discountTotal) * 0.20; // 20% VAT
    final totalAmount = subtotal - discountTotal + taxTotal;

    // Generate order number
    final orderNumber = await _generateOrderNumber();

    final order = Order(
      id: FirebaseService.generateId(),
      orderNumber: orderNumber,
      clientId: client.id,
      clientName: client.name,
      commercialId: commercial.id,
      commercialName: commercial.fullName,
      lineItems: lineItems,
      subtotal: subtotal,
      discountTotal: discountTotal,
      taxTotal: taxTotal,
      totalAmount: totalAmount,
      status: OrderStatus.pending,
      orderDate: DateTime.now(),
      deliveryDate: deliveryDate,
      notes: notes,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Save to Firestore
    await FirebaseService.setDocument(
      FirebaseService.ordersCollection.doc(order.id),
      order.toMap(),
    );

    // Update stock quantities
    await _updateStockQuantities(lineItems);

    return order;
  }

  // Update order status
  Future<void> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    await FirebaseService.updateDocument(
      FirebaseService.ordersCollection.doc(orderId),
      {
        'status': newStatus.name,
        'updatedAt': FieldValue.serverTimestamp(),
      },
    );
  }

  // Update order
  Future<void> updateOrder(String orderId, Map<String, dynamic> updates) async {
    final updateData = {
      ...updates,
      'updatedAt': FieldValue.serverTimestamp(),
    };

    await FirebaseService.updateDocument(
      FirebaseService.ordersCollection.doc(orderId),
      updateData,
    );
  }

  // Delete order (only if pending)
  Future<void> deleteOrder(String orderId) async {
    final orderDoc = await FirebaseService.getDocument(
      FirebaseService.ordersCollection.doc(orderId),
    );

    if (!orderDoc.exists) {
      throw Exception('Commande non trouvée');
    }

    final order = Order.fromMap(orderDoc.data() as Map<String, dynamic>);
    
    if (order.status != OrderStatus.pending) {
      throw Exception('Seules les commandes en attente peuvent être supprimées');
    }

    // Restore stock quantities
    await _restoreStockQuantities(order.lineItems);

    // Delete the order
    await FirebaseService.deleteDocument(
      FirebaseService.ordersCollection.doc(orderId),
    );
  }

  // Get order by ID
  Future<Order?> getOrderById(String orderId) async {
    final doc = await FirebaseService.getDocument(
      FirebaseService.ordersCollection.doc(orderId),
    );

    if (doc.exists) {
      return Order.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }

  // Get orders by client
  Future<List<Order>> getOrdersByClient(String clientId) async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.ordersCollection,
      queryBuilder: (query) => query
          .where('clientId', isEqualTo: clientId)
          .orderBy('createdAt', descending: true),
    );

    return snapshot.docs
        .map((doc) => Order.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Get orders by status
  Future<List<Order>> getOrdersByStatus(OrderStatus status) async {
    final currentUserData = await AuthService.getCurrentUserData();
    if (currentUserData == null) {
      throw Exception('Utilisateur non authentifié');
    }

    final commercial = currentUserData['data'] as Commercial;

    final snapshot = await FirebaseService.getCollection(
      FirebaseService.ordersCollection,
      queryBuilder: (query) => query
          .where('commercialId', isEqualTo: commercial.id)
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true),
    );

    return snapshot.docs
        .map((doc) => Order.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Convert quote to order
  Future<Order> convertQuoteToOrder(Quote quote) async {
    final client = await _getClientById(quote.clientId);
    if (client == null) {
      throw Exception('Client non trouvé');
    }

    final order = await createOrder(
      client: client,
      lineItems: quote.lineItems,
      notes: 'Converti du devis ${quote.quoteNumber}',
    );

    // Update quote status
    await FirebaseService.updateDocument(
      FirebaseService.quotesCollection.doc(quote.id),
      {
        'convertedToOrder': true,
        'orderId': order.id,
        'status': QuoteStatus.accepted.name,
        'updatedAt': FieldValue.serverTimestamp(),
      },
    );

    return order;
  }

  // Private helper methods
  Future<void> _validateStockAvailability(List<OrderLineItem> lineItems) async {
    for (final item in lineItems) {
      final productDoc = await FirebaseService.getDocument(
        FirebaseService.productsCollection.doc(item.productId),
      );

      if (!productDoc.exists) {
        throw Exception('Produit ${item.productName} non trouvé');
      }

      final product = Product.fromMap(productDoc.data() as Map<String, dynamic>);
      
      if (!product.isActive) {
        throw Exception('Produit ${item.productName} non disponible');
      }

      if (product.stockQuantity < item.quantity) {
        throw Exception(
          'Stock insuffisant pour ${item.productName}. '
          'Disponible: ${product.stockQuantity}, Demandé: ${item.quantity}',
        );
      }
    }
  }

  Future<void> _updateStockQuantities(List<OrderLineItem> lineItems) async {
    final batch = FirebaseService.batch();

    for (final item in lineItems) {
      final productRef = FirebaseService.productsCollection.doc(item.productId);
      batch.update(productRef, {
        'stockQuantity': FieldValue.increment(-item.quantity),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }

    await FirebaseService.commitBatch(batch);
  }

  Future<void> _restoreStockQuantities(List<OrderLineItem> lineItems) async {
    final batch = FirebaseService.batch();

    for (final item in lineItems) {
      final productRef = FirebaseService.productsCollection.doc(item.productId);
      batch.update(productRef, {
        'stockQuantity': FieldValue.increment(item.quantity),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }

    await FirebaseService.commitBatch(batch);
  }

  Future<String> _generateOrderNumber() async {
    final now = DateTime.now();
    final prefix = 'CMD${now.year}${now.month.toString().padLeft(2, '0')}';
    
    // Get the count of orders for this month
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);
    
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.ordersCollection,
      queryBuilder: (query) => query
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('createdAt', isLessThan: Timestamp.fromDate(endOfMonth)),
    );

    final orderCount = snapshot.docs.length + 1;
    return '$prefix${orderCount.toString().padLeft(4, '0')}';
  }

  Future<Client?> _getClientById(String clientId) async {
    final doc = await FirebaseService.getDocument(
      FirebaseService.clientsCollection.doc(clientId),
    );

    if (doc.exists) {
      return Client.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }
}

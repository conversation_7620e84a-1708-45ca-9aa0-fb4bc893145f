class AppConstants {
  // App Information
  static const String appName = 'VitaBrosse Mobile';
  static const String appVersion = '1.0.0';
  static const String companyName = 'VitaBrosse';
  
  // Firebase Collections
  static const String commercialsCollection = 'commercials';
  static const String merchandizersCollection = 'merchandizers';
  static const String clientsCollection = 'clients';
  static const String productsCollection = 'products';
  static const String ordersCollection = 'orders';
  static const String quotesCollection = 'quotes';
  static const String missionsCollection = 'missions';
  static const String reportsCollection = 'reports';
  static const String territoriesCollection = 'territories';
  
  // User Roles
  static const String adminRole = 'admin';
  static const String commercialRole = 'commercial';
  static const String merchandizerRole = 'merchandizer';
  
  // Account Status
  static const String activeStatus = 'active';
  static const String inactiveStatus = 'inactive';
  static const String suspendedStatus = 'suspended';
  static const String pendingActivationStatus = 'pending_activation';
  
  // Mission Status
  static const String pendingMissionStatus = 'pending';
  static const String inProgressMissionStatus = 'in_progress';
  static const String completedMissionStatus = 'completed';
  static const String cancelledMissionStatus = 'cancelled';
  
  // Mission Priority
  static const String lowPriority = 'low';
  static const String mediumPriority = 'medium';
  static const String highPriority = 'high';
  static const String urgentPriority = 'urgent';
  
  // Quote Status
  static const String draftQuoteStatus = 'draft';
  static const String sentQuoteStatus = 'sent';
  static const String viewedQuoteStatus = 'viewed';
  static const String acceptedQuoteStatus = 'accepted';
  static const String rejectedQuoteStatus = 'rejected';
  static const String expiredQuoteStatus = 'expired';
  
  // Order Status
  static const String pendingOrderStatus = 'pending';
  static const String confirmedOrderStatus = 'confirmed';
  static const String processingOrderStatus = 'processing';
  static const String shippedOrderStatus = 'shipped';
  static const String deliveredOrderStatus = 'delivered';
  static const String cancelledOrderStatus = 'cancelled';
  
  // Report Status
  static const String draftReportStatus = 'draft';
  static const String submittedReportStatus = 'submitted';
  static const String approvedReportStatus = 'approved';
  static const String rejectedReportStatus = 'rejected';
  
  // Photo Categories
  static const String beforePhotoCategory = 'before';
  static const String afterPhotoCategory = 'after';
  static const String issuePhotoCategory = 'issue';
  static const String solutionPhotoCategory = 'solution';
  static const String displayPhotoCategory = 'display';
  
  // Permissions
  static const String manageOrdersPermission = 'MANAGE_ORDERS';
  static const String createQuotesPermission = 'CREATE_QUOTES';
  static const String sendWhatsappPermission = 'SEND_WHATSAPP';
  static const String manageMerchandizersPermission = 'MANAGE_MERCHANDIZERS';
  static const String createMissionsPermission = 'CREATE_MISSIONS';
  static const String viewReportsPermission = 'VIEW_REPORTS';
  static const String accessCalendarPermission = 'ACCESS_CALENDAR';
  static const String viewMissionsPermission = 'VIEW_MISSIONS';
  static const String updateMissionStatusPermission = 'UPDATE_MISSION_STATUS';
  static const String submitReportsPermission = 'SUBMIT_REPORTS';
  static const String uploadPhotosPermission = 'UPLOAD_PHOTOS';
  static const String accessOfflinePermission = 'ACCESS_OFFLINE';
  
  // Default Values
  static const int defaultSessionTimeout = 3600; // 1 hour in seconds
  static const double defaultMaxDiscountPercentage = 10.0;
  static const double defaultRequireApprovalAbove = 10000.0;
  static const int defaultMaxOfflineDays = 7;
  static const int defaultEstimatedMissionDuration = 120; // 2 hours in minutes
  
  // File Paths
  static const String profilePhotosPath = 'profile_photos';
  static const String missionPhotosPath = 'mission_photos';
  static const String quotePdfsPath = 'quote_pdfs';
  static const String documentsPath = 'documents';
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxUsernameLength = 50;
  static const int maxDescriptionLength = 500;
  static const int maxNotesLength = 1000;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double cardElevation = 2.0;
  
  // Animation Durations
  static const int shortAnimationDuration = 200;
  static const int mediumAnimationDuration = 300;
  static const int longAnimationDuration = 500;
  
  // Network
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // WhatsApp
  static const String whatsappBaseUrl = 'https://wa.me/';
  static const String whatsappBusinessUrl = 'https://business.whatsapp.com/';
  
  // Support
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+33 1 XX XX XX XX';
  static const String documentationUrl = 'https://docs.vitabrosse.com/mobile';
}

class AppColors {
  // Primary Colors
  static const int primaryColorValue = 0xFF2E7D32;
  static const int secondaryColorValue = 0xFF4CAF50;
  
  // Status Colors
  static const int successColorValue = 0xFF4CAF50;
  static const int warningColorValue = 0xFFFF9800;
  static const int errorColorValue = 0xFFF44336;
  static const int infoColorValue = 0xFF2196F3;
  
  // Text Colors
  static const int primaryTextColorValue = 0xFF212121;
  static const int secondaryTextColorValue = 0xFF757575;
  static const int hintTextColorValue = 0xFF9E9E9E;
  
  // Background Colors
  static const int backgroundColorValue = 0xFFFAFAFA;
  static const int surfaceColorValue = 0xFFFFFFFF;
  static const int cardColorValue = 0xFFFFFFFF;
}

class AppStrings {
  // Authentication
  static const String loginTitle = 'Connexion';
  static const String usernameLabel = 'Nom d\'utilisateur';
  static const String passwordLabel = 'Mot de passe';
  static const String loginButton = 'Se connecter';
  static const String logoutButton = 'Se déconnecter';
  static const String forgotPassword = 'Mot de passe oublié ?';
  static const String changePassword = 'Changer le mot de passe';
  static const String newPassword = 'Nouveau mot de passe';
  static const String confirmPassword = 'Confirmer le mot de passe';
  
  // Navigation
  static const String dashboard = 'Tableau de bord';
  static const String orders = 'Commandes';
  static const String quotes = 'Devis';
  static const String clients = 'Clients';
  static const String missions = 'Missions';
  static const String reports = 'Rapports';
  static const String calendar = 'Calendrier';
  static const String profile = 'Profil';
  static const String settings = 'Paramètres';
  
  // Common Actions
  static const String save = 'Enregistrer';
  static const String cancel = 'Annuler';
  static const String delete = 'Supprimer';
  static const String edit = 'Modifier';
  static const String view = 'Voir';
  static const String create = 'Créer';
  static const String update = 'Mettre à jour';
  static const String submit = 'Soumettre';
  static const String approve = 'Approuver';
  static const String reject = 'Rejeter';
  static const String send = 'Envoyer';
  
  // Status Messages
  static const String loading = 'Chargement...';
  static const String noData = 'Aucune donnée disponible';
  static const String error = 'Une erreur s\'est produite';
  static const String success = 'Opération réussie';
  static const String networkError = 'Erreur de connexion réseau';
  static const String unauthorized = 'Accès non autorisé';
  
  // Validation Messages
  static const String requiredField = 'Ce champ est obligatoire';
  static const String invalidEmail = 'Adresse email invalide';
  static const String passwordTooShort = 'Le mot de passe doit contenir au moins 8 caractères';
  static const String passwordsDoNotMatch = 'Les mots de passe ne correspondent pas';
  static const String invalidPhoneNumber = 'Numéro de téléphone invalide';
}

Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter pub get --no-example

## exception

StateError: Bad state: C:\flutter\flutter\bin\cache\dart-sdk\bin\dart pub --suppress-analytics deps --<PERSON><PERSON> failed
Null check operator used on a null value
package:pub/src/command/deps.dart 108:62               DepsCommand.runProtected
===== asynchronous gap ===========================
package:pub/src/command.dart 197:7                     PubCommand.run
===== asynchronous gap ===========================
package:args/command_runner.dart 212:13                CommandRunner.runCommand
===== asynchronous gap ===========================
package:dartdev/dartdev.dart 247:18                    DartdevRunner.runCommand
===== asynchronous gap ===========================
package:dartdev/dartdev.dart 47:16                     runDartdev
===== asynchronous gap ===========================
C:\b\s\w\ir\x\w\sdk\pkg\dartdev\bin\dartdev.dart 13:5  main
This is an unexpected error. The full log and other details are collected in:

    C:\Users\<USER>\AppData\Local\Pub\Cache\log\pub_log.txt

Consider creating an issue on https://github.com/dart-lang/pub/issues/new
and attaching the relevant parts of that log file.


```
#0      _DefaultPub.deps.fail (package:flutter_tools/src/dart/pub.dart:370:7)
#1      _DefaultPub.deps (package:flutter_tools/src/dart/pub.dart:378:7)
<asynchronous suspension>
#2      computeExclusiveDevDependencies (package:flutter_tools/src/compute_dev_dependencies.dart:21:43)
<asynchronous suspension>
#3      findPlugins (package:flutter_tools/src/flutter_plugins.dart:105:23)
<asynchronous suspension>
#4      refreshPluginsList (package:flutter_tools/src/flutter_plugins.dart:1109:32)
<asynchronous suspension>
#5      FlutterProject.ensureReadyForPlatformSpecificTooling (package:flutter_tools/src/project.dart:369:5)
<asynchronous suspension>
#6      PackagesGetCommand.runCommand (package:flutter_tools/src/commands/packages.dart:383:7)
<asynchronous suspension>
#7      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1553:27)
<asynchronous suspension>
#8      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#9      CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#10     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#11     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#12     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#13     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#14     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#15     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel beta, 3.31.0-0.1.pre, on Microsoft Windows [Version 10.0.26100.4349], locale en-US) [712ms]
    • Flutter version 3.31.0-0.1.pre on channel beta at C:\flutter\flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision fce807530a (4 months ago), 2025-03-17 16:47:17 +0000
    • Engine revision 50f226569f
    • Dart version 3.8.0 (build 3.8.0-171.0.dev)
    • DevTools version 2.44.0

[✓] Windows Version (11 Home Single Language 64-bit, 24H2, 2009) [4.5s]

[!] Android toolchain - develop for Android devices (Android SDK version 34.0.0) [684ms]
    • Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    ✗ cmdline-tools component is missing.
      Try installing or updating Android Studio.
      Alternatively, download the tools from https://developer.android.com/studio#command-line-tools-only and make sure to set the ANDROID_HOME environment variable.
      See https://developer.android.com/studio/command-line for more details.
    ✗ Android license status unknown.
      Run `flutter doctor --android-licenses` to accept the SDK licenses.
      See https://flutter.dev/to/windows-android-setup for more details.

[✓] Chrome - develop for the web [437ms]
    • Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[!] Visual Studio - develop Windows apps (Visual Studio Community 2022 17.14.8) [435ms]
    • Visual Studio at C:\Program Files\Microsoft Visual Studio\2022\Community
    • Visual Studio Community 2022 version 17.14.36301.6
    ✗ Visual Studio is missing necessary components. Please re-run the Visual Studio installer for the "Desktop development with C++" workload, and include these components:
        MSVC v142 - VS 2019 C++ x64/x86 build tools
         - If there are multiple build tool versions available, install the latest
        C++ CMake tools for Windows
        Windows 10 SDK

[✓] Android Studio (version 2022.1) [86ms]
    • Android Studio at C:\Program Files\Android\Android Studio
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 11.0.15+0-b2043.56-9505619)

[✓] IntelliJ IDEA Ultimate Edition (version 2025.1) [82ms]
    • IntelliJ at C:\Program Files\JetBrains\IntelliJ IDEA 2025.1
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin version 251.25410.28

[✓] VS Code (version 1.102.1) [16ms]
    • VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    • Flutter extension version 3.114.0

[✓] Connected device (3 available) [260ms]
    • Windows (desktop) • windows • windows-x64    • Microsoft Windows [Version 10.0.26100.4349]
    • Chrome (web)      • chrome  • web-javascript • Google Chrome 138.0.7204.157
    • Edge (web)        • edge    • web-javascript • Microsoft Edge 138.0.3351.83

[✓] Network resources [439ms]
    • All expected network resources are available.

! Doctor found issues in 2 categories.
```

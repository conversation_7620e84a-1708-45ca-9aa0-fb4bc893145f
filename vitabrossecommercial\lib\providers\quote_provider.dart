import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/business_models.dart' as business;
import '../services/firebase/firebase_service.dart';
import '../services/quote_service.dart';
import '../services/pdf_service.dart';
import '../services/whatsapp_service.dart';
import 'auth_provider.dart';

// Quote service provider
final quoteServiceProvider = Provider<QuoteService>((ref) {
  return QuoteService();
});

// PDF service provider
final pdfServiceProvider = Provider<PdfService>((ref) {
  return PdfService();
});

// WhatsApp service provider
final whatsappServiceProvider = Provider<WhatsAppService>((ref) {
  return WhatsAppService();
});

// All quotes stream provider
final quotesStreamProvider = StreamProvider<List<business.Quote>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.quotesCollection,
    queryBuilder: (query) => query
        .where('commercialId', isEqualTo: commercialData.id)
        .orderBy('createdAt', descending: true),
  );

  await for (final snapshot in stream) {
    final quotes = snapshot.docs
        .map((doc) => business.Quote.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield quotes;
  }
});

// Quotes by status provider
final quotesByStatusProvider = Provider.family<List<business.Quote>, business.QuoteStatus>((ref, status) {
  final quotes = ref.watch(quotesStreamProvider);
  return quotes.when(
    data: (quotesList) => quotesList.where((quote) => quote.status == status).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Pending quotes provider
final pendingQuotesProvider = Provider<List<business.Quote>>((ref) {
  return ref.watch(quotesByStatusProvider(business.QuoteStatus.pending));
});

// Recent quotes provider (last 20)
final recentQuotesProvider = Provider<List<business.Quote>>((ref) {
  final quotes = ref.watch(quotesStreamProvider);
  return quotes.when(
    data: (quotesList) => quotesList.take(20).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Quote statistics provider
final quoteStatisticsProvider = Provider<QuoteStatistics>((ref) {
  final quotes = ref.watch(quotesStreamProvider);
  return quotes.when(
    data: (quotesList) => QuoteStatistics.calculate(quotesList),
    loading: () => QuoteStatistics.empty(),
    error: (_, __) => QuoteStatistics.empty(),
  );
});

// Single quote provider
final quoteProvider = StreamProvider.family<business.Quote?, String>((ref, quoteId) async* {
  final stream = FirebaseService.documentStream(
    FirebaseService.quotesCollection.doc(quoteId),
  );

  await for (final snapshot in stream) {
    if (snapshot.exists) {
      yield business.Quote.fromMap(snapshot.data() as Map<String, dynamic>);
    } else {
      yield null;
    }
  }
});

// Quote creation state provider
final quoteCreationProvider = StateNotifierProvider<QuoteCreationNotifier, QuoteCreationState>((ref) {
  final quoteService = ref.watch(quoteServiceProvider);
  return QuoteCreationNotifier(quoteService);
});

// Quote creation state
class QuoteCreationState {
  final bool isLoading;
  final String? error;
  final business.Quote? createdQuote;
  final List<business.OrderLineItem> lineItems;
  final business.Client? selectedClient;
  final double subtotal;
  final double discountTotal;
  final double taxTotal;
  final double totalAmount;
  final DateTime? validUntil;

  const QuoteCreationState({
    this.isLoading = false,
    this.error,
    this.createdQuote,
    this.lineItems = const [],
    this.selectedClient,
    this.subtotal = 0.0,
    this.discountTotal = 0.0,
    this.taxTotal = 0.0,
    this.totalAmount = 0.0,
    this.validUntil,
  });

  QuoteCreationState copyWith({
    bool? isLoading,
    String? error,
    business.Quote? createdQuote,
    List<business.OrderLineItem>? lineItems,
    business.Client? selectedClient,
    double? subtotal,
    double? discountTotal,
    double? taxTotal,
    double? totalAmount,
    DateTime? validUntil,
  }) {
    return QuoteCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdQuote: createdQuote ?? this.createdQuote,
      lineItems: lineItems ?? this.lineItems,
      selectedClient: selectedClient ?? this.selectedClient,
      subtotal: subtotal ?? this.subtotal,
      discountTotal: discountTotal ?? this.discountTotal,
      taxTotal: taxTotal ?? this.taxTotal,
      totalAmount: totalAmount ?? this.totalAmount,
      validUntil: validUntil ?? this.validUntil,
    );
  }
}

// Quote creation notifier
class QuoteCreationNotifier extends StateNotifier<QuoteCreationState> {
  final QuoteService _quoteService;

  QuoteCreationNotifier(this._quoteService) : super(const QuoteCreationState());

  void selectClient(business.Client client) {
    state = state.copyWith(selectedClient: client);
  }

  void addLineItem(business.OrderLineItem item) {
    final updatedItems = [...state.lineItems, item];
    state = state.copyWith(lineItems: updatedItems);
    _calculateTotals();
  }

  void updateLineItem(int index, business.OrderLineItem item) {
    final updatedItems = [...state.lineItems];
    updatedItems[index] = item;
    state = state.copyWith(lineItems: updatedItems);
    _calculateTotals();
  }

  void removeLineItem(int index) {
    final updatedItems = [...state.lineItems];
    updatedItems.removeAt(index);
    state = state.copyWith(lineItems: updatedItems);
    _calculateTotals();
  }

  void clearLineItems() {
    state = state.copyWith(lineItems: []);
    _calculateTotals();
  }

  void setValidUntil(DateTime? date) {
    state = state.copyWith(validUntil: date);
  }

  void _calculateTotals() {
    final subtotal = state.lineItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.unitPrice * item.quantity),
    );
    
    final discountTotal = state.lineItems.fold<double>(
      0.0,
      (sum, item) => sum + item.discount,
    );
    
    final taxTotal = (subtotal - discountTotal) * 0.20; // 20% VAT
    final totalAmount = subtotal - discountTotal + taxTotal;

    state = state.copyWith(
      subtotal: subtotal,
      discountTotal: discountTotal,
      taxTotal: taxTotal,
      totalAmount: totalAmount,
    );
  }

  Future<void> createQuote({
    required String notes,
  }) async {
    if (state.selectedClient == null || state.lineItems.isEmpty) {
      state = state.copyWith(error: 'Client et articles requis');
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final quote = await _quoteService.createQuote(
        client: state.selectedClient!,
        lineItems: state.lineItems,
        notes: notes,
        validUntil: state.validUntil,
      );

      state = state.copyWith(
        isLoading: false,
        createdQuote: quote,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void reset() {
    state = const QuoteCreationState();
  }
}

// Quote statistics model
class QuoteStatistics {
  final int totalQuotes;
  final int pendingQuotes;
  final int acceptedQuotes;
  final int rejectedQuotes;
  final int expiredQuotes;
  final double totalValue;
  final double averageQuoteValue;
  final double conversionRate;
  final double pendingValue;

  const QuoteStatistics({
    required this.totalQuotes,
    required this.pendingQuotes,
    required this.acceptedQuotes,
    required this.rejectedQuotes,
    required this.expiredQuotes,
    required this.totalValue,
    required this.averageQuoteValue,
    required this.conversionRate,
    required this.pendingValue,
  });

  factory QuoteStatistics.empty() {
    return const QuoteStatistics(
      totalQuotes: 0,
      pendingQuotes: 0,
      acceptedQuotes: 0,
      rejectedQuotes: 0,
      expiredQuotes: 0,
      totalValue: 0.0,
      averageQuoteValue: 0.0,
      conversionRate: 0.0,
      pendingValue: 0.0,
    );
  }

  factory QuoteStatistics.calculate(List<business.Quote> quotes) {
    final totalQuotes = quotes.length;
    final pendingQuotes = quotes.where((q) => q.status == business.QuoteStatus.pending).length;
    final acceptedQuotes = quotes.where((q) => q.status == business.QuoteStatus.accepted).length;
    final rejectedQuotes = quotes.where((q) => q.status == business.QuoteStatus.rejected).length;
    final expiredQuotes = quotes.where((q) => q.status == business.QuoteStatus.expired).length;
    
    final totalValue = quotes.fold<double>(0.0, (sum, quote) => sum + quote.totalAmount);
    final averageQuoteValue = totalQuotes > 0 ? totalValue / totalQuotes : 0.0;
    final conversionRate = totalQuotes > 0 ? (acceptedQuotes / totalQuotes) * 100 : 0.0;
    
    final pendingValue = quotes
        .where((q) => q.status == business.QuoteStatus.pending)
        .fold<double>(0.0, (sum, quote) => sum + quote.totalAmount);

    return QuoteStatistics(
      totalQuotes: totalQuotes,
      pendingQuotes: pendingQuotes,
      acceptedQuotes: acceptedQuotes,
      rejectedQuotes: rejectedQuotes,
      expiredQuotes: expiredQuotes,
      totalValue: totalValue,
      averageQuoteValue: averageQuoteValue,
      conversionRate: conversionRate,
      pendingValue: pendingValue,
    );
  }
}

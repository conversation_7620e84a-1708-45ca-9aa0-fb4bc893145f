import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/business_models.dart' as business;
import '../models/user_models.dart';
import '../services/firebase/firebase_service.dart';
import '../services/firebase/auth_service.dart';
import '../constants/app_constants.dart';

class QuoteService {
  // Create a new quote
  Future<business.Quote> createQuote({
    required business.Client client,
    required List<business.OrderLineItem> lineItems,
    required String notes,
    DateTime? validUntil,
  }) async {
    final currentUserData = await AuthService.getCurrentUserData();
    if (currentUserData == null ||
        currentUserData['role'] != AppConstants.commercialRole) {
      throw Exception('Seuls les commerciaux peuvent créer des devis');
    }

    final commercial = currentUserData['data'] as Commercial;

    // Calculate totals
    final subtotal = lineItems.fold<double>(
      0.0,
      (total, item) => total + (item.unitPrice * item.quantity),
    );

    final discountTotal = lineItems.fold<double>(
      0.0,
      (total, item) => total + item.discount,
    );

    final taxTotal = (subtotal - discountTotal) * 0.20; // 20% VAT
    final totalAmount = subtotal - discountTotal + taxTotal;

    // Generate quote number
    final quoteNumber = await _generateQuoteNumber();

    // Set default valid until date (30 days from now)
    final defaultValidUntil =
        validUntil ?? DateTime.now().add(const Duration(days: 30));

    final quote = business.Quote(
      id: FirebaseService.generateId(),
      quoteNumber: quoteNumber,
      clientId: client.id,
      clientName: client.name,
      clientEmail: client.email,
      clientPhone: client.phone,
      commercialId: commercial.id,
      commercialName: commercial.fullName,
      lineItems: lineItems,
      subtotal: subtotal,
      discountTotal: discountTotal,
      taxTotal: taxTotal,
      totalAmount: totalAmount,
      status: business.QuoteStatus.pending,
      quoteDate: DateTime.now(),
      validUntil: defaultValidUntil,
      notes: notes,
      whatsappSent: false,
      clientWhatsapp: client.phone, // Use phone as WhatsApp number
      convertedToOrder: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Save to Firestore
    await FirebaseService.setDocument(
      FirebaseService.quotesCollection.doc(quote.id),
      quote.toMap(),
    );

    return quote;
  }

  // Update quote status
  Future<void> updateQuoteStatus(
      String quoteId, business.QuoteStatus newStatus) async {
    await FirebaseService.updateDocument(
      FirebaseService.quotesCollection.doc(quoteId),
      {
        'status': newStatus.name,
        'updatedAt': FieldValue.serverTimestamp(),
      },
    );
  }

  // Update quote
  Future<void> updateQuote(String quoteId, Map<String, dynamic> updates) async {
    final updateData = {
      ...updates,
      'updatedAt': FieldValue.serverTimestamp(),
    };

    await FirebaseService.updateDocument(
      FirebaseService.quotesCollection.doc(quoteId),
      updateData,
    );
  }

  // Delete quote (only if pending)
  Future<void> deleteQuote(String quoteId) async {
    final quoteDoc = await FirebaseService.getDocument(
      FirebaseService.quotesCollection.doc(quoteId),
    );

    if (!quoteDoc.exists) {
      throw Exception('Devis non trouvé');
    }

    final quote =
        business.Quote.fromMap(quoteDoc.data() as Map<String, dynamic>);

    if (quote.status != business.QuoteStatus.pending) {
      throw Exception('Seuls les devis en attente peuvent être supprimés');
    }

    // Delete the quote
    await FirebaseService.deleteDocument(
      FirebaseService.quotesCollection.doc(quoteId),
    );
  }

  // Get quote by ID
  Future<business.Quote?> getQuoteById(String quoteId) async {
    final doc = await FirebaseService.getDocument(
      FirebaseService.quotesCollection.doc(quoteId),
    );

    if (doc.exists) {
      return business.Quote.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }

  // Get quotes by client
  Future<List<business.Quote>> getQuotesByClient(String clientId) async {
    final snapshot = await FirebaseService.getCollection(
      FirebaseService.quotesCollection,
      queryBuilder: (query) => query
          .where('clientId', isEqualTo: clientId)
          .orderBy('createdAt', descending: true),
    );

    return snapshot.docs
        .map(
            (doc) => business.Quote.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Get quotes by status
  Future<List<business.Quote>> getQuotesByStatus(
      business.QuoteStatus status) async {
    final currentUserData = await AuthService.getCurrentUserData();
    if (currentUserData == null) {
      throw Exception('Utilisateur non authentifié');
    }

    final commercial = currentUserData['data'] as Commercial;

    final snapshot = await FirebaseService.getCollection(
      FirebaseService.quotesCollection,
      queryBuilder: (query) => query
          .where('commercialId', isEqualTo: commercial.id)
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true),
    );

    return snapshot.docs
        .map(
            (doc) => business.Quote.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
  }

  // Mark quote as expired
  Future<void> markQuoteAsExpired(String quoteId) async {
    await updateQuoteStatus(quoteId, business.QuoteStatus.expired);
  }

  // Check and update expired quotes
  Future<void> checkAndUpdateExpiredQuotes() async {
    final currentUserData = await AuthService.getCurrentUserData();
    if (currentUserData == null) return;

    final commercial = currentUserData['data'] as Commercial;
    final now = DateTime.now();

    final snapshot = await FirebaseService.getCollection(
      FirebaseService.quotesCollection,
      queryBuilder: (query) => query
          .where('commercialId', isEqualTo: commercial.id)
          .where('status', isEqualTo: business.QuoteStatus.pending.name)
          .where('validUntil', isLessThan: Timestamp.fromDate(now)),
    );

    final batch = FirebaseService.batch();

    for (final doc in snapshot.docs) {
      batch.update(doc.reference, {
        'status': business.QuoteStatus.expired.name,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }

    if (snapshot.docs.isNotEmpty) {
      await FirebaseService.commitBatch(batch);
    }
  }

  // Duplicate quote
  Future<business.Quote> duplicateQuote(String quoteId) async {
    final originalQuote = await getQuoteById(quoteId);
    if (originalQuote == null) {
      throw Exception('Devis original non trouvé');
    }

    final client = await _getClientById(originalQuote.clientId);
    if (client == null) {
      throw Exception('Client non trouvé');
    }

    return await createQuote(
      client: client,
      lineItems: originalQuote.lineItems,
      notes: 'Copie du devis ${originalQuote.quoteNumber}',
      validUntil: DateTime.now().add(const Duration(days: 30)),
    );
  }

  // Private helper methods
  Future<String> _generateQuoteNumber() async {
    final now = DateTime.now();
    final prefix = 'DEV${now.year}${now.month.toString().padLeft(2, '0')}';

    // Get the count of quotes for this month
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);

    final snapshot = await FirebaseService.getCollection(
      FirebaseService.quotesCollection,
      queryBuilder: (query) => query
          .where('createdAt',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('createdAt', isLessThan: Timestamp.fromDate(endOfMonth)),
    );

    final quoteCount = snapshot.docs.length + 1;
    return '$prefix${quoteCount.toString().padLeft(4, '0')}';
  }

  Future<business.Client?> _getClientById(String clientId) async {
    final doc = await FirebaseService.getDocument(
      FirebaseService.clientsCollection.doc(clientId),
    );

    if (doc.exists) {
      return business.Client.fromMap(doc.data() as Map<String, dynamic>);
    }
    return null;
  }
}

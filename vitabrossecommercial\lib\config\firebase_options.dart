// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyC6MHdesCLSpuv2vTmuD2102Zzsjmk_K8k',
    appId: '1:87916737597:web:ad58f9e416a0c11f86e654',
    messagingSenderId: '87916737597',
    projectId: 'commercialapp-12451',
    authDomain: 'commercialapp-12451.firebaseapp.com',
    storageBucket: 'commercialapp-12451.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyC6MHdesCLSpuv2vTmuD2102Zzsjmk_K8k',
    appId: '1:87916737597:android:ad58f9e416a0c11f86e654',
    messagingSenderId: '87916737597',
    projectId: 'commercialapp-12451',
    storageBucket: 'commercialapp-12451.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC6MHdesCLSpuv2vTmuD2102Zzsjmk_K8k',
    appId: '1:87916737597:ios:ad58f9e416a0c11f86e654',
    messagingSenderId: '87916737597',
    projectId: 'commercialapp-12451',
    storageBucket: 'commercialapp-12451.firebasestorage.app',
    iosBundleId: 'com.example.commercial',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC6MHdesCLSpuv2vTmuD2102Zzsjmk_K8k',
    appId: '1:87916737597:macos:ad58f9e416a0c11f86e654',
    messagingSenderId: '87916737597',
    projectId: 'commercialapp-12451',
    storageBucket: 'commercialapp-12451.firebasestorage.app',
    iosBundleId: 'com.example.commercial',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyC6MHdesCLSpuv2vTmuD2102Zzsjmk_K8k',
    appId: '1:87916737597:windows:ad58f9e416a0c11f86e654',
    messagingSenderId: '87916737597',
    projectId: 'commercialapp-12451',
    storageBucket: 'commercialapp-12451.firebasestorage.app',
  );
}

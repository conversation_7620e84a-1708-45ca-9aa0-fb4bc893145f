import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase/auth_service.dart';
import '../models/user_models.dart';
import '../constants/app_constants.dart';

// Auth state provider
final authStateProvider = StreamProvider<User?>((ref) {
  return AuthService.authStateChanges;
});

// Current user data provider
final currentUserDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final authState = ref.watch(authStateProvider);
  
  return authState.when(
    data: (user) async {
      if (user == null) return null;
      return await AuthService.getCurrentUserData();
    },
    loading: () => null,
    error: (error, stack) => null,
  );
});

// Auth controller
class AuthController extends StateNotifier<AuthState> {
  AuthController() : super(const AuthState.initial());

  // Sign in with username and password
  Future<void> signInWithUsername({
    required String username,
    required String password,
    bool rememberMe = false,
  }) async {
    state = const AuthState.loading();
    
    try {
      final credential = await AuthService.signInWithUsername(
        username: username,
        password: password,
      );
      
      if (credential.user != null) {
        await AuthService.saveCredentials(
          username: username,
          rememberMe: rememberMe,
        );
        
        // Check if password change is required
        final passwordChangeRequired = await AuthService.isPasswordChangeRequired();
        
        if (passwordChangeRequired) {
          state = const AuthState.passwordChangeRequired();
        } else {
          state = AuthState.authenticated(credential.user!);
        }
      }
    } on FirebaseAuthException catch (e) {
      state = AuthState.error(e.message ?? 'Erreur d\'authentification');
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    state = const AuthState.loading();
    
    try {
      await AuthService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );
      
      final user = AuthService.currentUser;
      if (user != null) {
        state = AuthState.authenticated(user);
      }
    } on FirebaseAuthException catch (e) {
      state = AuthState.error(e.message ?? 'Erreur lors du changement de mot de passe');
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Sign out
  Future<void> signOut() async {
    state = const AuthState.loading();
    
    try {
      await AuthService.signOut();
      state = const AuthState.unauthenticated();
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  // Get saved credentials
  Future<Map<String, dynamic>> getSavedCredentials() async {
    return await AuthService.getSavedCredentials();
  }

  // Check biometric availability
  Future<bool> isBiometricAvailable() async {
    return await AuthService.isBiometricAvailable();
  }

  // Authenticate with biometrics
  Future<bool> authenticateWithBiometrics() async {
    return await AuthService.authenticateWithBiometrics();
  }

  // Reset state
  void resetState() {
    state = const AuthState.initial();
  }
}

// Auth state
sealed class AuthState {
  const AuthState();

  const factory AuthState.initial() = AuthInitial;
  const factory AuthState.loading() = AuthLoading;
  const factory AuthState.authenticated(User user) = AuthAuthenticated;
  const factory AuthState.unauthenticated() = AuthUnauthenticated;
  const factory AuthState.passwordChangeRequired() = AuthPasswordChangeRequired;
  const factory AuthState.error(String message) = AuthError;
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final User user;
  const AuthAuthenticated(this.user);
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthPasswordChangeRequired extends AuthState {
  const AuthPasswordChangeRequired();
}

class AuthError extends AuthState {
  final String message;
  const AuthError(this.message);
}

// Auth controller provider
final authControllerProvider = StateNotifierProvider<AuthController, AuthState>((ref) {
  return AuthController();
});

// User role provider
final userRoleProvider = FutureProvider<String?>((ref) async {
  final userData = await ref.watch(currentUserDataProvider.future);
  return userData?['role'] as String?;
});

// Commercial data provider
final commercialDataProvider = FutureProvider<Commercial?>((ref) async {
  final userData = await ref.watch(currentUserDataProvider.future);
  if (userData?['role'] == AppConstants.commercialRole) {
    return userData?['data'] as Commercial?;
  }
  return null;
});

// Merchandizer data provider
final merchandizerDataProvider = FutureProvider<Merchandizer?>((ref) async {
  final userData = await ref.watch(currentUserDataProvider.future);
  if (userData?['role'] == AppConstants.merchandizerRole) {
    return userData?['data'] as Merchandizer?;
  }
  return null;
});

// Permission checker providers
final hasPermissionProvider = Provider.family<bool, String>((ref, permission) {
  final userData = ref.watch(currentUserDataProvider);
  
  return userData.when(
    data: (data) {
      if (data == null) return false;
      
      final role = data['role'] as String?;
      if (role == AppConstants.adminRole) return true;
      
      final userAccount = data['data'];
      if (userAccount is Commercial) {
        return userAccount.account.permissions.contains(permission);
      } else if (userAccount is Merchandizer) {
        return userAccount.account.permissions.contains(permission);
      }
      
      return false;
    },
    loading: () => false,
    error: (error, stack) => false,
  );
});

// Specific permission providers
final canManageOrdersProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.manageOrdersPermission));
});

final canCreateQuotesProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.createQuotesPermission));
});

final canSendWhatsappProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.sendWhatsappPermission));
});

final canManageMerchandizersProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.manageMerchandizersPermission));
});

final canCreateMissionsProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.createMissionsPermission));
});

final canViewReportsProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.viewReportsPermission));
});

final canAccessOfflineProvider = Provider<bool>((ref) {
  return ref.watch(hasPermissionProvider(AppConstants.accessOfflinePermission));
});

// User territory provider
final userTerritoryProvider = FutureProvider<String?>((ref) async {
  final userData = await ref.watch(currentUserDataProvider.future);
  if (userData == null) return null;
  
  final userAccount = userData['data'];
  if (userAccount is Commercial) {
    return userAccount.territory;
  } else if (userAccount is Merchandizer) {
    return userAccount.territory;
  }
  
  return null;
});

// Is admin provider
final isAdminProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role.when(
    data: (role) => role == AppConstants.adminRole,
    loading: () => false,
    error: (error, stack) => false,
  );
});

// Is commercial provider
final isCommercialProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role.when(
    data: (role) => role == AppConstants.commercialRole,
    loading: () => false,
    error: (error, stack) => false,
  );
});

// Is merchandizer provider
final isMerchandizerProvider = Provider<bool>((ref) {
  final role = ref.watch(userRoleProvider);
  return role.when(
    data: (role) => role == AppConstants.merchandizerRole,
    loading: () => false,
    error: (error, stack) => false,
  );
});

// Account status provider
final accountStatusProvider = FutureProvider<String?>((ref) async {
  final userData = await ref.watch(currentUserDataProvider.future);
  if (userData == null) return null;
  
  final userAccount = userData['data'];
  if (userAccount is Commercial) {
    return userAccount.account.accountStatus;
  } else if (userAccount is Merchandizer) {
    return userAccount.account.accountStatus;
  }
  
  return null;
});

// Session timeout provider
final sessionTimeoutProvider = FutureProvider<int>((ref) async {
  final userData = await ref.watch(currentUserDataProvider.future);
  if (userData == null) return AppConstants.defaultSessionTimeout;
  
  final userAccount = userData['data'];
  if (userAccount is Commercial) {
    return userAccount.account.sessionTimeout;
  } else if (userAccount is Merchandizer) {
    return userAccount.account.sessionTimeout;
  }
  
  return AppConstants.defaultSessionTimeout;
});

import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';

class RecentItemsCard extends StatelessWidget {
  final String title;
  final List<Map<String, dynamic>> items;
  final VoidCallback? onViewAll;
  final int maxItems;

  const RecentItemsCard({
    super.key,
    required this.title,
    required this.items,
    this.onViewAll,
    this.maxItems = 5,
  });

  @override
  Widget build(BuildContext context) {
    final displayItems = items.take(maxItems).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('Voir tout'),
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            if (displayItems.isEmpty)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.inbox_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Aucun élément',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: displayItems.length,
                separatorBuilder: (context, index) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final item = displayItems[index];
                  return _buildItemTile(context, item);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemTile(BuildContext context, Map<String, dynamic> item) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      title: Text(
        item['title'] ?? '',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: item['subtitle'] != null
          ? Text(
              item['subtitle'],
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: const Color(AppColors.secondaryTextColorValue),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      trailing: item['trailing'] != null
          ? Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: (item['color'] as Color?)?.withValues(alpha: 0.1) ?? 
                       Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                item['trailing'],
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: item['color'] as Color? ?? 
                         const Color(AppColors.secondaryTextColorValue),
                ),
              ),
            )
          : null,
      onTap: item['onTap'] as VoidCallback?,
    );
  }
}

class ActivityCard extends StatelessWidget {
  final String title;
  final List<ActivityItem> activities;
  final VoidCallback? onViewAll;
  final int maxItems;

  const ActivityCard({
    super.key,
    required this.title,
    required this.activities,
    this.onViewAll,
    this.maxItems = 5,
  });

  @override
  Widget build(BuildContext context) {
    final displayActivities = activities.take(maxItems).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: const Text('Voir tout'),
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            if (displayActivities.isEmpty)
              Container(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.timeline,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Aucune activité récente',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: displayActivities.length,
                itemBuilder: (context, index) {
                  final activity = displayActivities[index];
                  return _buildActivityTile(context, activity);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityTile(BuildContext context, ActivityItem activity) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: activity.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              activity.icon,
              color: activity.color,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                if (activity.subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    activity.subtitle!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: const Color(AppColors.secondaryTextColorValue),
                    ),
                  ),
                ],
                
                const SizedBox(height: 4),
                
                Text(
                  activity.timeAgo,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: const Color(AppColors.hintTextColorValue),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ActivityItem {
  final String title;
  final String? subtitle;
  final String timeAgo;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;

  const ActivityItem({
    required this.title,
    this.subtitle,
    required this.timeAgo,
    required this.icon,
    required this.color,
    this.onTap,
  });
}

class SummaryCard extends StatelessWidget {
  final String title;
  final List<SummaryItem> items;
  final Color? headerColor;

  const SummaryCard({
    super.key,
    required this.title,
    required this.items,
    this.headerColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            decoration: BoxDecoration(
              color: headerColor?.withValues(alpha: 0.1) ?? 
                     const Color(AppColors.primaryColorValue).withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.defaultBorderRadius),
                topRight: Radius.circular(AppConstants.defaultBorderRadius),
              ),
            ),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: headerColor ?? const Color(AppColors.primaryColorValue),
              ),
            ),
          ),
          
          Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              children: items.map((item) => _buildSummaryItem(context, item)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(BuildContext context, SummaryItem item) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            item.label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            item.value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: item.color ?? const Color(AppColors.primaryTextColorValue),
            ),
          ),
        ],
      ),
    );
  }
}

class SummaryItem {
  final String label;
  final String value;
  final Color? color;

  const SummaryItem({
    required this.label,
    required this.value,
    this.color,
  });
}

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function getUserRole() {
      return request.auth.token.role;
    }
    
    function isAdmin() {
      return isAuthenticated() && getUserRole() == 'admin';
    }
    
    function isCommercial() {
      return isAuthenticated() && getUserRole() == 'commercial';
    }
    
    function isMerchandizer() {
      return isAuthenticated() && getUserRole() == 'merchandizer';
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Commercials collection
    match /commercials/{commercialId} {
      allow read, write: if isAdmin();
      allow read, update: if isCommercial() && isOwner(commercialId);
    }
    
    // Merchandizers collection
    match /merchandizers/{merchandizerId} {
      allow read, write: if isAdmin();
      allow read, update: if isMerchandizer() && isOwner(merchandizerId);
      allow read: if isCommercial() && 
        request.auth.uid in resource.data.supervisedBy;
    }
    
    // Clients collection - access limited by territory/assignment
    match /clients/{clientId} {
      allow read, write: if isAdmin();
      allow read, write: if isCommercial() && 
        request.auth.uid in resource.data.assignedCommercials;
    }
    
    // Products collection - read access for all authenticated users
    match /products/{productId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // Orders collection
    match /orders/{orderId} {
      allow read, write: if isAdmin();
      allow read, write: if isCommercial() && 
        request.auth.uid == resource.data.commercialId;
    }
    
    // Quotes collection
    match /quotes/{quoteId} {
      allow read, write: if isAdmin();
      allow read, write: if isCommercial() && 
        request.auth.uid == resource.data.commercialId;
    }
    
    // Missions collection
    match /missions/{missionId} {
      allow read, write: if isAdmin();
      
      // Commercial can manage missions they created
      allow read, write: if isCommercial() && 
        request.auth.uid == resource.data.createdBy;
      
      // Merchandizer can read and update missions assigned to them
      allow read, update: if isMerchandizer() && 
        request.auth.uid == resource.data.merchandizerId;
    }
    
    // Reports collection
    match /reports/{reportId} {
      allow read, write: if isAdmin();
      
      // Merchandizer can create and update their own reports
      allow create: if isMerchandizer() && 
        request.auth.uid == request.resource.data.merchandizerId;
      
      allow read, update: if isMerchandizer() && 
        request.auth.uid == resource.data.merchandizerId;
      
      // Commercial can read and approve reports from their merchandizers
      allow read, update: if isCommercial() && 
        request.auth.uid == resource.data.reviewedBy;
    }
    
    // Territories collection - read access for authenticated users
    match /territories/{territoryId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }
    
    // User activity logs (optional)
    match /activity_logs/{logId} {
      allow create: if isAuthenticated();
      allow read: if isAdmin();
      allow read: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
    }
    
    // System settings (admin only)
    match /settings/{settingId} {
      allow read, write: if isAdmin();
    }
    
    // Notifications
    match /notifications/{notificationId} {
      allow read, update: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      allow create: if isAuthenticated();
      allow delete: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
    }
    
    // Default deny rule
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

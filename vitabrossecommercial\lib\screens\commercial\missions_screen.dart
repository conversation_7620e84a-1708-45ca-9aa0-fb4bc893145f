import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';

class CommercialMissionsScreen extends ConsumerWidget {
  const CommercialMissionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Missions'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // TODO: Navigate to create mission
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment,
              size: 64,
              color: Color(AppColors.infoColorValue),
            ),
            SizedBox(height: 16),
            Text(
              'Écran des missions',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text(
              'À implémenter dans la prochaine tâche',
              style: TextStyle(
                color: Color(AppColors.secondaryTextColorValue),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart' as business;
import '../../providers/quote_provider.dart';
import '../../services/pdf_service.dart';
import '../../services/whatsapp_service.dart';
import '../../widgets/common/loading_button.dart';

class QuoteDetailsScreen extends ConsumerStatefulWidget {
  final String quoteId;

  const QuoteDetailsScreen({
    super.key,
    required this.quoteId,
  });

  @override
  ConsumerState<QuoteDetailsScreen> createState() => _QuoteDetailsScreenState();
}

class _QuoteDetailsScreenState extends ConsumerState<QuoteDetailsScreen> {
  bool _isUpdatingStatus = false;
  bool _isSendingWhatsApp = false;
  bool _isGeneratingPdf = false;

  @override
  Widget build(BuildContext context) {
    final quoteAsync = ref.watch(quoteProvider(widget.quoteId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Détails du devis'),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showMoreOptions(context);
            },
          ),
        ],
      ),
      body: quoteAsync.when(
        data: (quote) {
          if (quote == null) {
            return const Center(
              child: Text('Devis non trouvé'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Quote Header
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              quote.quoteNumber,
                              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: _getStatusColor(quote.status).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                _getStatusText(quote.status),
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: _getStatusColor(quote.status),
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        Row(
                          children: [
                            Icon(
                              Icons.person,
                              size: 20,
                              color: const Color(AppColors.secondaryTextColorValue),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Client: ${quote.clientName}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 20,
                              color: const Color(AppColors.secondaryTextColorValue),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Créé le: ${_formatDate(quote.quoteDate)}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 8),
                        
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 20,
                              color: const Color(AppColors.secondaryTextColorValue),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Valable jusqu\'au: ${_formatDate(quote.validUntil)}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ],
                        ),
                        
                        if (quote.whatsappSent) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                size: 20,
                                color: const Color(AppColors.successColorValue),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Envoyé par WhatsApp le ${_formatDate(quote.whatsappSentDate!)}',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: const Color(AppColors.successColorValue),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Line Items
                Text(
                  'Articles du devis',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                Card(
                  child: Column(
                    children: [
                      ...quote.lineItems.map((item) => _buildLineItemTile(item)),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Quote Summary
                Text(
                  'Résumé',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      children: [
                        _buildSummaryRow('Sous-total', '${quote.subtotal.toStringAsFixed(2)}€'),
                        _buildSummaryRow('Remise', '-${quote.discountTotal.toStringAsFixed(2)}€'),
                        _buildSummaryRow('TVA', '${quote.taxTotal.toStringAsFixed(2)}€'),
                        const Divider(),
                        _buildSummaryRow(
                          'Total',
                          '${quote.totalAmount.toStringAsFixed(2)}€',
                          isTotal: true,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Notes
                if (quote.notes.isNotEmpty) ...[
                  Text(
                    'Notes',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Text(
                        quote.notes,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                ],
                
                // Action Buttons
                Text(
                  'Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // WhatsApp and PDF Actions
                Row(
                  children: [
                    Expanded(
                      child: LoadingButton(
                        onPressed: () => _sendViaWhatsApp(quote),
                        isLoading: _isSendingWhatsApp,
                        text: 'WhatsApp',
                        icon: Icons.message,
                        backgroundColor: const Color(0xFF25D366),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: LoadingButton(
                        onPressed: () => _generatePdf(quote),
                        isLoading: _isGeneratingPdf,
                        text: 'PDF',
                        icon: Icons.picture_as_pdf,
                        backgroundColor: const Color(AppColors.errorColorValue),
                        isOutlined: true,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // Status Actions
                if (quote.status == business.QuoteStatus.pending) ...[
                  Row(
                    children: [
                      Expanded(
                        child: LoadingButton(
                          onPressed: () => _updateQuoteStatus(quote, business.QuoteStatus.accepted),
                          isLoading: _isUpdatingStatus,
                          text: 'Accepter',
                          backgroundColor: const Color(AppColors.successColorValue),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: LoadingButton(
                          onPressed: () => _updateQuoteStatus(quote, business.QuoteStatus.rejected),
                          isLoading: _isUpdatingStatus,
                          text: 'Refuser',
                          backgroundColor: const Color(AppColors.errorColorValue),
                          isOutlined: true,
                        ),
                      ),
                    ],
                  ),
                ],
                
                const SizedBox(height: 32),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Color(AppColors.errorColorValue),
              ),
              const SizedBox(height: 16),
              Text(
                'Erreur de chargement',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLineItemTile(business.OrderLineItem item) {
    return ListTile(
      title: Text(
        item.productName,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Quantité: ${item.quantity}'),
          Text('Prix unitaire: ${item.unitPrice.toStringAsFixed(2)}€'),
          if (item.discount > 0)
            Text(
              'Remise: -${item.discount.toStringAsFixed(2)}€',
              style: const TextStyle(
                color: Color(AppColors.errorColorValue),
              ),
            ),
        ],
      ),
      trailing: Text(
        '${item.total.toStringAsFixed(2)}€',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 16,
          color: Color(AppColors.primaryColorValue),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? const Color(AppColors.primaryColorValue) : null,
            ),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.copy),
            title: const Text('Dupliquer'),
            onTap: () {
              Navigator.pop(context);
              _duplicateQuote();
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete),
            title: const Text('Supprimer'),
            onTap: () {
              Navigator.pop(context);
              _deleteQuote();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _sendViaWhatsApp(business.Quote quote) async {
    setState(() {
      _isSendingWhatsApp = true;
    });

    try {
      final whatsappService = ref.read(whatsappServiceProvider);
      final success = await whatsappService.sendQuoteViaWhatsApp(quote: quote);
      
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Devis envoyé via WhatsApp!'),
            backgroundColor: Color(AppColors.successColorValue),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSendingWhatsApp = false;
        });
      }
    }
  }

  Future<void> _generatePdf(business.Quote quote) async {
    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      final pdfService = ref.read(pdfServiceProvider);
      final pdfBytes = await pdfService.generateQuotePdf(quote);
      await pdfService.sharePdf(pdfBytes, 'devis_${quote.quoteNumber}.pdf');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF généré et partagé!'),
            backgroundColor: Color(AppColors.successColorValue),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGeneratingPdf = false;
        });
      }
    }
  }

  Future<void> _updateQuoteStatus(business.Quote quote, business.QuoteStatus newStatus) async {
    setState(() {
      _isUpdatingStatus = true;
    });

    try {
      final quoteService = ref.read(quoteServiceProvider);
      await quoteService.updateQuoteStatus(quote.id, newStatus);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Statut mis à jour: ${_getStatusText(newStatus)}'),
            backgroundColor: const Color(AppColors.successColorValue),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingStatus = false;
        });
      }
    }
  }

  void _duplicateQuote() {
    // TODO: Implement quote duplication
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Duplication de devis à implémenter'),
        backgroundColor: Color(AppColors.infoColorValue),
      ),
    );
  }

  void _deleteQuote() {
    // TODO: Implement quote deletion
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Suppression de devis à implémenter'),
        backgroundColor: Color(AppColors.infoColorValue),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Color _getStatusColor(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return const Color(AppColors.warningColorValue);
      case business.QuoteStatus.sent:
        return const Color(AppColors.infoColorValue);
      case business.QuoteStatus.viewed:
        return const Color(AppColors.primaryColorValue);
      case business.QuoteStatus.accepted:
        return const Color(AppColors.successColorValue);
      case business.QuoteStatus.rejected:
        return const Color(AppColors.errorColorValue);
      case business.QuoteStatus.expired:
        return Colors.grey;
    }
  }

  String _getStatusText(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return 'En attente';
      case business.QuoteStatus.sent:
        return 'Envoyé';
      case business.QuoteStatus.viewed:
        return 'Vu';
      case business.QuoteStatus.accepted:
        return 'Accepté';
      case business.QuoteStatus.rejected:
        return 'Refusé';
      case business.QuoteStatus.expired:
        return 'Expiré';
    }
  }
}

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:local_auth/local_auth.dart';
import '../../models/user_models.dart';
import '../../constants/app_constants.dart';
import 'firebase_service.dart';

class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final LocalAuthentication _localAuth = LocalAuthentication();

  // Current user stream
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Current user
  static User? get currentUser => _auth.currentUser;
  static String? get currentUserId => _auth.currentUser?.uid;

  // Sign in with email and password
  static Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // Update last login timestamp
      if (credential.user != null) {
        await _updateLastLogin(credential.user!.uid);
      }
      
      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign in with username and password (custom implementation)
  static Future<UserCredential> signInWithUsername({
    required String username,
    required String password,
  }) async {
    try {
      // First, find the user by username
      final userDoc = await _findUserByUsername(username);
      if (userDoc == null) {
        throw FirebaseAuthException(
          code: 'user-not-found',
          message: 'Nom d\'utilisateur non trouvé.',
        );
      }

      final userData = userDoc.data() as Map<String, dynamic>;
      final email = userData['email'] as String;

      // Check account status
      final accountStatus = userData['account']['accountStatus'] as String;
      if (accountStatus == AppConstants.suspendedStatus) {
        throw FirebaseAuthException(
          code: 'user-disabled',
          message: 'Compte suspendu. Contactez votre administrateur.',
        );
      }

      if (accountStatus == AppConstants.inactiveStatus) {
        throw FirebaseAuthException(
          code: 'user-disabled',
          message: 'Compte inactif. Contactez votre administrateur.',
        );
      }

      // Sign in with email and password
      final credential = await signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Check if password change is required
      if (accountStatus == AppConstants.pendingActivationStatus) {
        // Mark for password change
        await _markPasswordChangeRequired(credential.user!.uid);
      }

      return credential;
    } catch (e) {
      if (e is FirebaseAuthException) {
        rethrow;
      }
      throw FirebaseAuthException(
        code: 'unknown',
        message: 'Erreur de connexion: ${e.toString()}',
      );
    }
  }

  // Change password
  static Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw FirebaseAuthException(
          code: 'no-current-user',
          message: 'Aucun utilisateur connecté.',
        );
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);

      // Update account status if it was pending activation
      await _updateAccountStatusAfterPasswordChange(user.uid);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign out
  static Future<void> signOut() async {
    try {
      await _clearLocalData();
      await _auth.signOut();
    } catch (e) {
      throw FirebaseAuthException(
        code: 'sign-out-error',
        message: 'Erreur lors de la déconnexion: ${e.toString()}',
      );
    }
  }

  // Get current user data
  static Future<Map<String, dynamic>?> getCurrentUserData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return null;

      // Try commercial first
      final commercialDoc = await FirebaseService.commercialsCollection
          .doc(user.uid)
          .get();
      
      if (commercialDoc.exists) {
        return {
          'role': AppConstants.commercialRole,
          'data': Commercial.fromMap(commercialDoc.data() as Map<String, dynamic>),
        };
      }

      // Try merchandizer
      final merchandizerDoc = await FirebaseService.merchandizersCollection
          .doc(user.uid)
          .get();
      
      if (merchandizerDoc.exists) {
        return {
          'role': AppConstants.merchandizerRole,
          'data': Merchandizer.fromMap(merchandizerDoc.data() as Map<String, dynamic>),
        };
      }

      return null;
    } catch (e) {
      throw FirebaseException(
        plugin: 'auth_service',
        message: 'Erreur lors de la récupération des données utilisateur: ${e.toString()}',
      );
    }
  }

  // Biometric authentication
  static Future<bool> isBiometricAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  static Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  static Future<bool> authenticateWithBiometrics() async {
    try {
      return await _localAuth.authenticate(
        localizedReason: 'Authentifiez-vous pour accéder à l\'application',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
    } catch (e) {
      return false;
    }
  }

  // Remember me functionality
  static Future<void> saveCredentials({
    required String username,
    bool rememberMe = false,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    if (rememberMe) {
      await prefs.setString('saved_username', username);
      await prefs.setBool('remember_me', true);
    } else {
      await prefs.remove('saved_username');
      await prefs.setBool('remember_me', false);
    }
  }

  static Future<Map<String, dynamic>> getSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'username': prefs.getString('saved_username') ?? '',
      'rememberMe': prefs.getBool('remember_me') ?? false,
    };
  }

  // Private helper methods
  static Future<DocumentSnapshot?> _findUserByUsername(String username) async {
    // Search in commercials
    final commercialQuery = await FirebaseService.commercialsCollection
        .where('account.username', isEqualTo: username)
        .limit(1)
        .get();
    
    if (commercialQuery.docs.isNotEmpty) {
      return commercialQuery.docs.first;
    }

    // Search in merchandizers
    final merchandizerQuery = await FirebaseService.merchandizersCollection
        .where('account.username', isEqualTo: username)
        .limit(1)
        .get();
    
    if (merchandizerQuery.docs.isNotEmpty) {
      return merchandizerQuery.docs.first;
    }

    return null;
  }

  static Future<void> _updateLastLogin(String userId) async {
    final batch = FirebaseService.batch();
    
    // Update in commercials collection
    final commercialRef = FirebaseService.commercialsCollection.doc(userId);
    batch.update(commercialRef, {
      'lastLogin': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Update in merchandizers collection
    final merchandizerRef = FirebaseService.merchandizersCollection.doc(userId);
    batch.update(merchandizerRef, {
      'lastLogin': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    try {
      await batch.commit();
    } catch (e) {
      // Ignore errors for last login update
    }
  }

  static Future<void> _markPasswordChangeRequired(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('password_change_required', true);
  }

  static Future<void> _updateAccountStatusAfterPasswordChange(String userId) async {
    final batch = FirebaseService.batch();
    
    // Update in commercials collection
    final commercialRef = FirebaseService.commercialsCollection.doc(userId);
    batch.update(commercialRef, {
      'account.accountStatus': AppConstants.activeStatus,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Update in merchandizers collection
    final merchandizerRef = FirebaseService.merchandizersCollection.doc(userId);
    batch.update(merchandizerRef, {
      'account.accountStatus': AppConstants.activeStatus,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    try {
      await batch.commit();
      
      // Clear password change requirement
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('password_change_required');
    } catch (e) {
      // Handle error
    }
  }

  static Future<void> _clearLocalData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('password_change_required');
    // Clear other local data as needed
  }

  static FirebaseAuthException _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return FirebaseAuthException(
          code: e.code,
          message: 'Utilisateur non trouvé.',
        );
      case 'wrong-password':
        return FirebaseAuthException(
          code: e.code,
          message: 'Mot de passe incorrect.',
        );
      case 'user-disabled':
        return FirebaseAuthException(
          code: e.code,
          message: 'Compte désactivé. Contactez votre administrateur.',
        );
      case 'too-many-requests':
        return FirebaseAuthException(
          code: e.code,
          message: 'Trop de tentatives. Veuillez réessayer plus tard.',
        );
      case 'weak-password':
        return FirebaseAuthException(
          code: e.code,
          message: 'Le mot de passe est trop faible.',
        );
      case 'requires-recent-login':
        return FirebaseAuthException(
          code: e.code,
          message: 'Veuillez vous reconnecter pour effectuer cette action.',
        );
      default:
        return FirebaseAuthException(
          code: e.code,
          message: e.message ?? 'Erreur d\'authentification.',
        );
    }
  }

  // Check if password change is required
  static Future<bool> isPasswordChangeRequired() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('password_change_required') ?? false;
  }
}

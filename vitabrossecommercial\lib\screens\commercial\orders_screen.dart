import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart';
import '../../providers/order_provider.dart';

import '../../widgets/common/custom_text_field.dart';
import '../../widgets/cards/performance_card.dart';
import 'create_order_screen.dart';
import 'order_details_screen.dart';

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({super.key});

  @override
  ConsumerState<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  OrderStatus? _selectedStatus;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ordersAsync = ref.watch(ordersStreamProvider);
    final orderStats = ref.watch(orderStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Commandes'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateOrderScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              children: [
                // Search Bar
                CustomSearchField(
                  controller: _searchController,
                  hint: 'Rechercher une commande...',
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),

                const SizedBox(height: 12),

                // Status Filter
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'Toutes',
                        _selectedStatus == null,
                        () => setState(() => _selectedStatus = null),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'En attente',
                        _selectedStatus == OrderStatus.pending,
                        () => setState(
                            () => _selectedStatus = OrderStatus.pending),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Confirmées',
                        _selectedStatus == OrderStatus.confirmed,
                        () => setState(
                            () => _selectedStatus = OrderStatus.confirmed),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'En cours',
                        _selectedStatus == OrderStatus.processing,
                        () => setState(
                            () => _selectedStatus = OrderStatus.processing),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Expédiées',
                        _selectedStatus == OrderStatus.shipped,
                        () => setState(
                            () => _selectedStatus = OrderStatus.shipped),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Livrées',
                        _selectedStatus == OrderStatus.delivered,
                        () => setState(
                            () => _selectedStatus = OrderStatus.delivered),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Orders List
          Expanded(
            child: ordersAsync.when(
              data: (orders) {
                final filteredOrders = _filterOrders(orders);

                if (filteredOrders.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(ordersStreamProvider);
                  },
                  child: Column(
                    children: [
                      // Stats Summary
                      Container(
                        padding:
                            const EdgeInsets.all(AppConstants.defaultPadding),
                        child: Row(
                          children: [
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Total',
                                value: orderStats.totalOrders.toString(),
                                icon: Icons.shopping_cart,
                                color: const Color(AppColors.primaryColorValue),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CompactMetricCard(
                                title: 'En attente',
                                value: orderStats.pendingOrders.toString(),
                                icon: Icons.schedule,
                                color: const Color(AppColors.warningColorValue),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Chiffre d\'affaires',
                                value:
                                    '${orderStats.totalRevenue.toStringAsFixed(0)}€',
                                icon: Icons.euro,
                                color: const Color(AppColors.successColorValue),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Orders List
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                          ),
                          itemCount: filteredOrders.length,
                          itemBuilder: (context, index) {
                            final order = filteredOrders[index];
                            return _buildOrderCard(context, order);
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Color(AppColors.errorColorValue),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Erreur de chargement',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.invalidate(ordersStreamProvider);
                      },
                      child: const Text('Réessayer'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Order> _filterOrders(List<Order> orders) {
    return orders.where((order) {
      final matchesSearch = _searchQuery.isEmpty ||
          order.orderNumber.toLowerCase().contains(_searchQuery) ||
          order.clientName.toLowerCase().contains(_searchQuery);

      final matchesStatus =
          _selectedStatus == null || order.status == _selectedStatus;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor:
          const Color(AppColors.primaryColorValue).withValues(alpha: 0.2),
      checkmarkColor: const Color(AppColors.primaryColorValue),
    );
  }

  Widget _buildOrderCard(BuildContext context, Order order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => OrderDetailsScreen(orderId: order.id),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          _getStatusColor(order.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getStatusIcon(order.status),
                      color: _getStatusColor(order.status),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.orderNumber,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          order.clientName,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: const Color(
                                        AppColors.secondaryTextColorValue),
                                  ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${order.totalAmount.toStringAsFixed(2)}€',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(AppColors.primaryColorValue),
                            ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(order.status)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getStatusText(order.status),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: _getStatusColor(order.status),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${order.orderDate.day}/${order.orderDate.month}/${order.orderDate.year}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.shopping_cart,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${order.lineItems.length} article(s)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                  ),
                  if (order.deliveryDate != null) ...[
                    const SizedBox(width: 16),
                    Icon(
                      Icons.local_shipping,
                      size: 16,
                      color: const Color(AppColors.secondaryTextColorValue),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Livraison: ${order.deliveryDate!.day}/${order.deliveryDate!.month}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                const Color(AppColors.secondaryTextColorValue),
                          ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune commande trouvée',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: const Color(AppColors.secondaryTextColorValue),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Créez votre première commande',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: const Color(AppColors.secondaryTextColorValue),
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateOrderScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Créer une commande'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return const Color(AppColors.warningColorValue);
      case OrderStatus.confirmed:
        return const Color(AppColors.infoColorValue);
      case OrderStatus.processing:
        return const Color(AppColors.primaryColorValue);
      case OrderStatus.shipped:
        return Colors.purple;
      case OrderStatus.delivered:
        return const Color(AppColors.successColorValue);
      case OrderStatus.cancelled:
        return const Color(AppColors.errorColorValue);
    }
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.schedule;
      case OrderStatus.confirmed:
        return Icons.check_circle_outline;
      case OrderStatus.processing:
        return Icons.autorenew;
      case OrderStatus.shipped:
        return Icons.local_shipping;
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'En attente';
      case OrderStatus.confirmed:
        return 'Confirmée';
      case OrderStatus.processing:
        return 'En cours';
      case OrderStatus.shipped:
        return 'Expédiée';
      case OrderStatus.delivered:
        return 'Livrée';
      case OrderStatus.cancelled:
        return 'Annulée';
    }
  }
}

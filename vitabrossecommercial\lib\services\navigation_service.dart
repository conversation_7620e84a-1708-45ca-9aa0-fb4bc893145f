import 'package:flutter/material.dart';
import '../screens/commercial/clients_screen.dart';
import '../screens/commercial/orders_screen.dart';
import '../screens/commercial/quotes_screen.dart';
import '../screens/commercial/missions_screen.dart';
import '../screens/merchandiser/merchandiser_missions_screen.dart';
import '../screens/merchandiser/reports_screen.dart';
import '../screens/shared/calendar_screen.dart';
import '../screens/shared/profile_screen.dart';

class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static BuildContext? get context => navigatorKey.currentContext;

  // Commercial Navigation
  static void navigateToClients() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const ClientsScreen(),
        ),
      );
    }
  }

  static void navigateToOrders() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const OrdersScreen(),
        ),
      );
    }
  }

  static void navigateToQuotes() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const QuotesScreen(),
        ),
      );
    }
  }

  static void navigateToCommercialMissions() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const CommercialMissionsScreen(),
        ),
      );
    }
  }

  // Merchandiser Navigation
  static void navigateToMerchandiserMissions() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const MerchandiserMissionsScreen(),
        ),
      );
    }
  }

  static void navigateToReports() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const ReportsScreen(),
        ),
      );
    }
  }

  // Shared Navigation
  static void navigateToCalendar() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const CalendarScreen(),
        ),
      );
    }
  }

  static void navigateToProfile() {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(
          builder: (context) => const ProfileScreen(),
        ),
      );
    }
  }

  // Generic navigation methods
  static void push(Widget screen) {
    if (context != null) {
      Navigator.of(context!).push(
        MaterialPageRoute(builder: (context) => screen),
      );
    }
  }

  static void pushReplacement(Widget screen) {
    if (context != null) {
      Navigator.of(context!).pushReplacement(
        MaterialPageRoute(builder: (context) => screen),
      );
    }
  }

  static void pushAndRemoveUntil(Widget screen) {
    if (context != null) {
      Navigator.of(context!).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => screen),
        (route) => false,
      );
    }
  }

  static void pop([dynamic result]) {
    if (context != null && Navigator.of(context!).canPop()) {
      Navigator.of(context!).pop(result);
    }
  }

  static void popUntil(String routeName) {
    if (context != null) {
      Navigator.of(context!).popUntil(ModalRoute.withName(routeName));
    }
  }

  // Dialog methods
  static Future<T?> showDialog<T>(Widget dialog) {
    if (context != null) {
      return showDialog<T>(
        context: context!,
        builder: (context) => dialog,
      );
    }
    return Future.value(null);
  }

  static Future<T?> showBottomSheet<T>(Widget bottomSheet) {
    if (context != null) {
      return showModalBottomSheet<T>(
        context: context!,
        builder: (context) => bottomSheet,
        isScrollControlled: true,
      );
    }
    return Future.value(null);
  }

  // Snackbar methods
  static void showSnackBar(String message, {
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 3),
  }) {
    if (context != null) {
      ScaffoldMessenger.of(context!).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: duration,
        ),
      );
    }
  }

  static void showSuccessSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.green,
    );
  }

  static void showErrorSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.red,
    );
  }

  static void showWarningSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.orange,
    );
  }

  static void showInfoSnackBar(String message) {
    showSnackBar(
      message,
      backgroundColor: Colors.blue,
    );
  }
}

// Route names constants
class Routes {
  static const String splash = '/';
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String changePassword = '/change-password';
  
  // Commercial routes
  static const String clients = '/clients';
  static const String clientDetails = '/client-details';
  static const String orders = '/orders';
  static const String orderDetails = '/order-details';
  static const String createOrder = '/create-order';
  static const String quotes = '/quotes';
  static const String quoteDetails = '/quote-details';
  static const String createQuote = '/create-quote';
  static const String missions = '/missions';
  static const String missionDetails = '/mission-details';
  static const String createMission = '/create-mission';
  static const String merchandizers = '/merchandizers';
  
  // Merchandiser routes
  static const String merchandiserMissions = '/merchandiser-missions';
  static const String reports = '/reports';
  static const String createReport = '/create-report';
  
  // Shared routes
  static const String calendar = '/calendar';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
}

// Route generator for named routes
class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case Routes.clients:
        return MaterialPageRoute(builder: (_) => const ClientsScreen());
      case Routes.orders:
        return MaterialPageRoute(builder: (_) => const OrdersScreen());
      case Routes.quotes:
        return MaterialPageRoute(builder: (_) => const QuotesScreen());
      case Routes.missions:
        return MaterialPageRoute(builder: (_) => const CommercialMissionsScreen());
      case Routes.merchandiserMissions:
        return MaterialPageRoute(builder: (_) => const MerchandiserMissionsScreen());
      case Routes.reports:
        return MaterialPageRoute(builder: (_) => const ReportsScreen());
      case Routes.calendar:
        return MaterialPageRoute(builder: (_) => const CalendarScreen());
      case Routes.profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('Route ${settings.name} not found'),
            ),
          ),
        );
    }
  }
}

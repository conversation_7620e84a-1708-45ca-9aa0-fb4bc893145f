import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/business_models.dart' as business;
import '../services/firebase/firebase_service.dart';
import '../services/order_service.dart';
import 'auth_provider.dart';

// Order service provider
final orderServiceProvider = Provider<OrderService>((ref) {
  return OrderService();
});

// All orders stream provider
final ordersStreamProvider = StreamProvider<List<business.Order>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.ordersCollection,
    queryBuilder: (query) => query
        .where('commercialId', isEqualTo: commercialData.id)
        .orderBy('createdAt', descending: true),
  );

  await for (final snapshot in stream) {
    final orders = snapshot.docs
        .map(
            (doc) => business.Order.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield orders;
  }
});

// Orders by status provider
final ordersByStatusProvider =
    Provider.family<List<business.Order>, business.OrderStatus>((ref, status) {
  final orders = ref.watch(ordersStreamProvider);
  return orders.when(
    data: (ordersList) =>
        ordersList.where((order) => order.status == status).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Pending orders provider
final pendingOrdersProvider = Provider<List<business.Order>>((ref) {
  return ref.watch(ordersByStatusProvider(business.OrderStatus.pending));
});

// Recent orders provider (last 20)
final recentOrdersProvider = Provider<List<business.Order>>((ref) {
  final orders = ref.watch(ordersStreamProvider);
  return orders.when(
    data: (ordersList) => ordersList.take(20).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Order statistics provider
final orderStatisticsProvider = Provider<OrderStatistics>((ref) {
  final orders = ref.watch(ordersStreamProvider);
  return orders.when(
    data: (ordersList) => OrderStatistics.calculate(ordersList),
    loading: () => OrderStatistics.empty(),
    error: (_, __) => OrderStatistics.empty(),
  );
});

// Single order provider
final orderProvider =
    StreamProvider.family<business.Order?, String>((ref, orderId) async* {
  final stream = FirebaseService.documentStream(
    FirebaseService.ordersCollection.doc(orderId),
  );

  await for (final snapshot in stream) {
    if (snapshot.exists) {
      yield business.Order.fromMap(snapshot.data() as Map<String, dynamic>);
    } else {
      yield null;
    }
  }
});

// Order creation state provider
final orderCreationProvider =
    StateNotifierProvider<OrderCreationNotifier, OrderCreationState>((ref) {
  final orderService = ref.watch(orderServiceProvider);
  return OrderCreationNotifier(orderService);
});

// Order creation state
class OrderCreationState {
  final bool isLoading;
  final String? error;
  final business.Order? createdOrder;
  final List<business.OrderLineItem> lineItems;
  final business.Client? selectedClient;
  final double subtotal;
  final double discountTotal;
  final double taxTotal;
  final double totalAmount;

  const OrderCreationState({
    this.isLoading = false,
    this.error,
    this.createdOrder,
    this.lineItems = const [],
    this.selectedClient,
    this.subtotal = 0.0,
    this.discountTotal = 0.0,
    this.taxTotal = 0.0,
    this.totalAmount = 0.0,
  });

  OrderCreationState copyWith({
    bool? isLoading,
    String? error,
    business.Order? createdOrder,
    List<business.OrderLineItem>? lineItems,
    business.Client? selectedClient,
    double? subtotal,
    double? discountTotal,
    double? taxTotal,
    double? totalAmount,
  }) {
    return OrderCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdOrder: createdOrder ?? this.createdOrder,
      lineItems: lineItems ?? this.lineItems,
      selectedClient: selectedClient ?? this.selectedClient,
      subtotal: subtotal ?? this.subtotal,
      discountTotal: discountTotal ?? this.discountTotal,
      taxTotal: taxTotal ?? this.taxTotal,
      totalAmount: totalAmount ?? this.totalAmount,
    );
  }
}

// Order creation notifier
class OrderCreationNotifier extends StateNotifier<OrderCreationState> {
  final OrderService _orderService;

  OrderCreationNotifier(this._orderService) : super(const OrderCreationState());

  void selectClient(business.Client client) {
    state = state.copyWith(selectedClient: client);
  }

  void addLineItem(business.OrderLineItem item) {
    final updatedItems = [...state.lineItems, item];
    state = state.copyWith(lineItems: updatedItems);
    _calculateTotals();
  }

  void updateLineItem(int index, business.OrderLineItem item) {
    final updatedItems = [...state.lineItems];
    updatedItems[index] = item;
    state = state.copyWith(lineItems: updatedItems);
    _calculateTotals();
  }

  void removeLineItem(int index) {
    final updatedItems = [...state.lineItems];
    updatedItems.removeAt(index);
    state = state.copyWith(lineItems: updatedItems);
    _calculateTotals();
  }

  void clearLineItems() {
    state = state.copyWith(lineItems: []);
    _calculateTotals();
  }

  void _calculateTotals() {
    final subtotal = state.lineItems.fold<double>(
      0.0,
      (sum, item) => sum + (item.unitPrice * item.quantity),
    );

    final discountTotal = state.lineItems.fold<double>(
      0.0,
      (sum, item) => sum + item.discount,
    );

    final taxTotal = (subtotal - discountTotal) * 0.20; // 20% VAT
    final totalAmount = subtotal - discountTotal + taxTotal;

    state = state.copyWith(
      subtotal: subtotal,
      discountTotal: discountTotal,
      taxTotal: taxTotal,
      totalAmount: totalAmount,
    );
  }

  Future<void> createOrder({
    required String notes,
    DateTime? deliveryDate,
  }) async {
    if (state.selectedClient == null || state.lineItems.isEmpty) {
      state = state.copyWith(error: 'Client et articles requis');
      return;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final order = await _orderService.createOrder(
        client: state.selectedClient!,
        lineItems: state.lineItems,
        notes: notes,
        deliveryDate: deliveryDate,
      );

      state = state.copyWith(
        isLoading: false,
        createdOrder: order,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void reset() {
    state = const OrderCreationState();
  }
}

// Order statistics model
class OrderStatistics {
  final int totalOrders;
  final int pendingOrders;
  final int confirmedOrders;
  final int deliveredOrders;
  final int cancelledOrders;
  final double totalRevenue;
  final double averageOrderValue;
  final double pendingValue;

  const OrderStatistics({
    required this.totalOrders,
    required this.pendingOrders,
    required this.confirmedOrders,
    required this.deliveredOrders,
    required this.cancelledOrders,
    required this.totalRevenue,
    required this.averageOrderValue,
    required this.pendingValue,
  });

  factory OrderStatistics.empty() {
    return const OrderStatistics(
      totalOrders: 0,
      pendingOrders: 0,
      confirmedOrders: 0,
      deliveredOrders: 0,
      cancelledOrders: 0,
      totalRevenue: 0.0,
      averageOrderValue: 0.0,
      pendingValue: 0.0,
    );
  }

  factory OrderStatistics.calculate(List<business.Order> orders) {
    final totalOrders = orders.length;
    final pendingOrders =
        orders.where((o) => o.status == business.OrderStatus.pending).length;
    final confirmedOrders =
        orders.where((o) => o.status == business.OrderStatus.confirmed).length;
    final deliveredOrders =
        orders.where((o) => o.status == business.OrderStatus.delivered).length;
    final cancelledOrders =
        orders.where((o) => o.status == business.OrderStatus.cancelled).length;

    final totalRevenue = orders
        .where((o) => o.status != business.OrderStatus.cancelled)
        .fold<double>(0.0, (sum, order) => sum + order.totalAmount);

    final averageOrderValue =
        totalOrders > 0 ? totalRevenue / totalOrders : 0.0;

    final pendingValue = orders
        .where((o) => o.status == business.OrderStatus.pending)
        .fold<double>(0.0, (sum, order) => sum + order.totalAmount);

    return OrderStatistics(
      totalOrders: totalOrders,
      pendingOrders: pendingOrders,
      confirmedOrders: confirmedOrders,
      deliveredOrders: deliveredOrders,
      cancelledOrders: cancelledOrders,
      totalRevenue: totalRevenue,
      averageOrderValue: averageOrderValue,
      pendingValue: pendingValue,
    );
  }
}

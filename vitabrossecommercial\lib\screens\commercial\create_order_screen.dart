import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart' as business;
import '../../providers/order_provider.dart';
import '../../providers/commercial_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_button.dart';

class CreateOrderScreen extends ConsumerStatefulWidget {
  const CreateOrderScreen({super.key});

  @override
  ConsumerState<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends ConsumerState<CreateOrderScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  DateTime? _selectedDeliveryDate;

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orderCreationState = ref.watch(orderCreationProvider);
    final clients = ref.watch(commercialClientsProvider);

    // Listen to order creation state changes
    ref.listen<OrderCreationState>(orderCreationProvider, (previous, next) {
      if (next.createdOrder != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Commande créée avec succès!'),
            backgroundColor: Color(AppColors.successColorValue),
          ),
        );
        Navigator.of(context).pop();
      } else if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouvelle Commande'),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(orderCreationProvider.notifier).reset();
            },
            child: const Text('Réinitialiser'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppConstants.defaultPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Client Selection
                    Text(
                      'Sélectionner un client',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),

                    const SizedBox(height: 12),

                    clients.when(
                      data: (clientsList) {
                        if (clientsList.isEmpty) {
                          return const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: Text('Aucun client disponible'),
                            ),
                          );
                        }

                        return Card(
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: DropdownButtonFormField<business.Client>(
                              value: orderCreationState.selectedClient,
                              decoration: const InputDecoration(
                                labelText: 'Client',
                                border: OutlineInputBorder(),
                              ),
                              items: clientsList.map((client) {
                                return DropdownMenuItem<business.Client>(
                                  value: client,
                                  child: Text(client.name),
                                );
                              }).toList(),
                              onChanged: (client) {
                                if (client != null) {
                                  ref
                                      .read(orderCreationProvider.notifier)
                                      .selectClient(client);
                                }
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Veuillez sélectionner un client';
                                }
                                return null;
                              },
                            ),
                          ),
                        );
                      },
                      loading: () => const Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      ),
                      error: (error, stack) => Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text('Erreur: $error'),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Line Items Section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Articles (${orderCreationState.lineItems.length})',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        ElevatedButton.icon(
                          onPressed: () {
                            _showAddProductDialog();
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Ajouter'),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    if (orderCreationState.lineItems.isEmpty)
                      const Card(
                        child: Padding(
                          padding: EdgeInsets.all(32),
                          child: Center(
                            child: Column(
                              children: [
                                Icon(
                                  Icons.shopping_cart_outlined,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Aucun article ajouté',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                    else
                      ...orderCreationState.lineItems
                          .asMap()
                          .entries
                          .map((entry) {
                        final index = entry.key;
                        final item = entry.value;
                        return _buildLineItemCard(item, index);
                      }),

                    const SizedBox(height: 24),

                    // Order Summary
                    if (orderCreationState.lineItems.isNotEmpty) ...[
                      Text(
                        'Résumé de la commande',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 12),
                      Card(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              _buildSummaryRow('Sous-total',
                                  '${orderCreationState.subtotal.toStringAsFixed(2)}€'),
                              _buildSummaryRow('Remise',
                                  '-${orderCreationState.discountTotal.toStringAsFixed(2)}€'),
                              _buildSummaryRow('TVA (20%)',
                                  '${orderCreationState.taxTotal.toStringAsFixed(2)}€'),
                              const Divider(),
                              _buildSummaryRow(
                                'Total',
                                '${orderCreationState.totalAmount.toStringAsFixed(2)}€',
                                isTotal: true,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Additional Information
                    Text(
                      'Informations complémentaires',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),

                    const SizedBox(height: 12),

                    // Delivery Date
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: CustomDateField(
                          controller: TextEditingController(
                            text: _selectedDeliveryDate != null
                                ? '${_selectedDeliveryDate!.day}/${_selectedDeliveryDate!.month}/${_selectedDeliveryDate!.year}'
                                : '',
                          ),
                          label: 'Date de livraison (optionnelle)',
                          selectedDate: _selectedDeliveryDate,
                          onDateSelected: (date) {
                            setState(() {
                              _selectedDeliveryDate = date;
                            });
                          },
                          firstDate: DateTime.now(),
                          lastDate:
                              DateTime.now().add(const Duration(days: 365)),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Notes
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child: CustomTextField(
                          controller: _notesController,
                          label: 'Notes (optionnelles)',
                          maxLines: 3,
                          hint: 'Instructions spéciales, commentaires...',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom Action Bar
            Container(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: LoadingButton(
                  onPressed: orderCreationState.selectedClient != null &&
                          orderCreationState.lineItems.isNotEmpty
                      ? _createOrder
                      : null,
                  isLoading: orderCreationState.isLoading,
                  text: 'Créer la commande',
                  width: double.infinity,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLineItemCard(business.OrderLineItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.productName,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Quantité: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)}€',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  if (item.discount > 0)
                    Text(
                      'Remise: -${item.discount.toStringAsFixed(2)}€',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: const Color(AppColors.errorColorValue),
                          ),
                    ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${item.total.toStringAsFixed(2)}€',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(AppColors.primaryColorValue),
                      ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () {
                        // TODO: Edit line item
                      },
                      icon: const Icon(Icons.edit, size: 20),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                    ),
                    IconButton(
                      onPressed: () {
                        ref
                            .read(orderCreationProvider.notifier)
                            .removeLineItem(index);
                      },
                      icon: const Icon(Icons.delete, size: 20),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                  color:
                      isTotal ? const Color(AppColors.primaryColorValue) : null,
                ),
          ),
        ],
      ),
    );
  }

  void _showAddProductDialog() {
    // TODO: Implement product selection dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Sélection de produits à implémenter'),
        backgroundColor: Color(AppColors.infoColorValue),
      ),
    );
  }

  Future<void> _createOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    await ref.read(orderCreationProvider.notifier).createOrder(
          notes: _notesController.text.trim(),
          deliveryDate: _selectedDeliveryDate,
        );
  }
}

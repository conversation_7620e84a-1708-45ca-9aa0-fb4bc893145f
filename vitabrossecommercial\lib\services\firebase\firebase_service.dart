import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../constants/app_constants.dart';

class FirebaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // Getters for Firebase instances
  static FirebaseFirestore get firestore => _firestore;
  static FirebaseAuth get auth => _auth;
  static FirebaseStorage get storage => _storage;

  // Current user
  static User? get currentUser => _auth.currentUser;
  static String? get currentUserId => _auth.currentUser?.uid;

  // Collection references
  static CollectionReference get commercialsCollection =>
      _firestore.collection(AppConstants.commercialsCollection);
  
  static CollectionReference get merchandizersCollection =>
      _firestore.collection(AppConstants.merchandizersCollection);
  
  static CollectionReference get clientsCollection =>
      _firestore.collection(AppConstants.clientsCollection);
  
  static CollectionReference get productsCollection =>
      _firestore.collection(AppConstants.productsCollection);
  
  static CollectionReference get ordersCollection =>
      _firestore.collection(AppConstants.ordersCollection);
  
  static CollectionReference get quotesCollection =>
      _firestore.collection(AppConstants.quotesCollection);
  
  static CollectionReference get missionsCollection =>
      _firestore.collection(AppConstants.missionsCollection);
  
  static CollectionReference get reportsCollection =>
      _firestore.collection(AppConstants.reportsCollection);
  
  static CollectionReference get territoriesCollection =>
      _firestore.collection(AppConstants.territoriesCollection);

  // Generic CRUD operations
  static Future<DocumentReference> addDocument(
    CollectionReference collection,
    Map<String, dynamic> data,
  ) async {
    try {
      return await collection.add(data);
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to add document: $e',
      );
    }
  }

  static Future<void> setDocument(
    DocumentReference document,
    Map<String, dynamic> data, {
    bool merge = false,
  }) async {
    try {
      await document.set(data, SetOptions(merge: merge));
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to set document: $e',
      );
    }
  }

  static Future<void> updateDocument(
    DocumentReference document,
    Map<String, dynamic> data,
  ) async {
    try {
      await document.update(data);
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to update document: $e',
      );
    }
  }

  static Future<void> deleteDocument(DocumentReference document) async {
    try {
      await document.delete();
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to delete document: $e',
      );
    }
  }

  static Future<DocumentSnapshot> getDocument(DocumentReference document) async {
    try {
      return await document.get();
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to get document: $e',
      );
    }
  }

  static Future<QuerySnapshot> getCollection(
    CollectionReference collection, {
    Query Function(Query)? queryBuilder,
  }) async {
    try {
      Query query = collection;
      if (queryBuilder != null) {
        query = queryBuilder(query);
      }
      return await query.get();
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to get collection: $e',
      );
    }
  }

  // Stream operations
  static Stream<DocumentSnapshot> documentStream(DocumentReference document) {
    return document.snapshots();
  }

  static Stream<QuerySnapshot> collectionStream(
    CollectionReference collection, {
    Query Function(Query)? queryBuilder,
  }) {
    Query query = collection;
    if (queryBuilder != null) {
      query = queryBuilder(query);
    }
    return query.snapshots();
  }

  // Batch operations
  static WriteBatch batch() => _firestore.batch();

  static Future<void> commitBatch(WriteBatch batch) async {
    try {
      await batch.commit();
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to commit batch: $e',
      );
    }
  }

  // Transaction operations
  static Future<T> runTransaction<T>(
    Future<T> Function(Transaction) updateFunction,
  ) async {
    try {
      return await _firestore.runTransaction(updateFunction);
    } catch (e) {
      throw FirebaseException(
        plugin: 'cloud_firestore',
        message: 'Failed to run transaction: $e',
      );
    }
  }

  // Storage operations
  static Reference storageRef(String path) => _storage.ref(path);

  static Future<String> uploadFile(
    String path,
    List<int> data, {
    String? contentType,
  }) async {
    try {
      final ref = _storage.ref(path);
      final uploadTask = ref.putData(
        data,
        SettableMetadata(contentType: contentType),
      );
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw FirebaseException(
        plugin: 'firebase_storage',
        message: 'Failed to upload file: $e',
      );
    }
  }

  static Future<void> deleteFile(String path) async {
    try {
      await _storage.ref(path).delete();
    } catch (e) {
      throw FirebaseException(
        plugin: 'firebase_storage',
        message: 'Failed to delete file: $e',
      );
    }
  }

  // Utility methods
  static String generateId() => _firestore.collection('temp').doc().id;

  static Timestamp timestampFromDate(DateTime date) => Timestamp.fromDate(date);

  static DateTime dateFromTimestamp(Timestamp timestamp) => timestamp.toDate();

  static Map<String, dynamic> addTimestamps(Map<String, dynamic> data) {
    final now = Timestamp.now();
    return {
      ...data,
      'createdAt': data['createdAt'] ?? now,
      'updatedAt': now,
    };
  }

  // Error handling
  static String getErrorMessage(dynamic error) {
    if (error is FirebaseException) {
      switch (error.code) {
        case 'permission-denied':
          return 'Vous n\'avez pas les permissions nécessaires pour cette action.';
        case 'not-found':
          return 'Document non trouvé.';
        case 'already-exists':
          return 'Ce document existe déjà.';
        case 'resource-exhausted':
          return 'Quota dépassé. Veuillez réessayer plus tard.';
        case 'unauthenticated':
          return 'Vous devez être connecté pour effectuer cette action.';
        case 'unavailable':
          return 'Service temporairement indisponible. Veuillez réessayer.';
        default:
          return error.message ?? 'Une erreur inconnue s\'est produite.';
      }
    }
    return error.toString();
  }

  // Network status
  static Future<bool> isOnline() async {
    try {
      await _firestore.enableNetwork();
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<void> enableOfflineMode() async {
    try {
      await _firestore.disableNetwork();
    } catch (e) {
      // Already offline or error
    }
  }

  static Future<void> enableOnlineMode() async {
    try {
      await _firestore.enableNetwork();
    } catch (e) {
      // Already online or error
    }
  }

  // Cache management
  static Future<void> clearCache() async {
    try {
      await _firestore.clearPersistence();
    } catch (e) {
      // Cache already cleared or error
    }
  }

  // Settings
  static void configureFirestore() {
    _firestore.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
    );
  }
}

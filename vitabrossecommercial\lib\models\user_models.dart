import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';

// Base User Account Model
class UserAccount {
  final String username;
  final String accountStatus;
  final List<String> permissions;
  final bool canAccessOffline;
  final int sessionTimeout;

  const UserAccount({
    required this.username,
    required this.accountStatus,
    required this.permissions,
    required this.canAccessOffline,
    required this.sessionTimeout,
  });

  Map<String, dynamic> toMap() {
    return {
      'username': username,
      'accountStatus': accountStatus,
      'permissions': permissions,
      'canAccessOffline': canAccessOffline,
      'sessionTimeout': sessionTimeout,
    };
  }

  factory UserAccount.fromMap(Map<String, dynamic> map) {
    return UserAccount(
      username: map['username'] ?? '',
      accountStatus: map['accountStatus'] ?? AppConstants.pendingActivationStatus,
      permissions: List<String>.from(map['permissions'] ?? []),
      canAccessOffline: map['canAccessOffline'] ?? false,
      sessionTimeout: map['sessionTimeout'] ?? AppConstants.defaultSessionTimeout,
    );
  }
}

// Commercial Model
class Commercial {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String employeeId;
  final String territory;
  final UserAccount account;
  final CommercialPermissions commercialPermissions;
  final List<String> assignedClients;
  final List<String> supervisedMerchandizers;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Commercial({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.employeeId,
    required this.territory,
    required this.account,
    required this.commercialPermissions,
    required this.assignedClients,
    required this.supervisedMerchandizers,
    required this.createdAt,
    required this.updatedAt,
  });

  String get fullName => '$firstName $lastName';

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'employeeId': employeeId,
      'territory': territory,
      'account': account.toMap(),
      'commercialPermissions': commercialPermissions.toMap(),
      'assignedClients': assignedClients,
      'supervisedMerchandizers': supervisedMerchandizers,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Commercial.fromMap(Map<String, dynamic> map) {
    return Commercial(
      id: map['id'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      employeeId: map['employeeId'] ?? '',
      territory: map['territory'] ?? '',
      account: UserAccount.fromMap(map['account'] ?? {}),
      commercialPermissions: CommercialPermissions.fromMap(map['commercialPermissions'] ?? {}),
      assignedClients: List<String>.from(map['assignedClients'] ?? []),
      supervisedMerchandizers: List<String>.from(map['supervisedMerchandizers'] ?? []),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Commercial copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? employeeId,
    String? territory,
    UserAccount? account,
    CommercialPermissions? commercialPermissions,
    List<String>? assignedClients,
    List<String>? supervisedMerchandizers,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Commercial(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      employeeId: employeeId ?? this.employeeId,
      territory: territory ?? this.territory,
      account: account ?? this.account,
      commercialPermissions: commercialPermissions ?? this.commercialPermissions,
      assignedClients: assignedClients ?? this.assignedClients,
      supervisedMerchandizers: supervisedMerchandizers ?? this.supervisedMerchandizers,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Commercial Permissions Model
class CommercialPermissions {
  final double maxDiscountPercentage;
  final double requireApprovalAbove;
  final bool canViewAllClients;

  const CommercialPermissions({
    required this.maxDiscountPercentage,
    required this.requireApprovalAbove,
    required this.canViewAllClients,
  });

  Map<String, dynamic> toMap() {
    return {
      'maxDiscountPercentage': maxDiscountPercentage,
      'requireApprovalAbove': requireApprovalAbove,
      'canViewAllClients': canViewAllClients,
    };
  }

  factory CommercialPermissions.fromMap(Map<String, dynamic> map) {
    return CommercialPermissions(
      maxDiscountPercentage: map['maxDiscountPercentage']?.toDouble() ?? AppConstants.defaultMaxDiscountPercentage,
      requireApprovalAbove: map['requireApprovalAbove']?.toDouble() ?? AppConstants.defaultRequireApprovalAbove,
      canViewAllClients: map['canViewAllClients'] ?? false,
    );
  }
}

// Merchandizer Model
class Merchandizer {
  final String id;
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final String territory;
  final UserAccount account;
  final MerchandizerPermissions merchandizerPermissions;
  final String managerId;
  final String managerName;
  final List<String> assignedStores;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Merchandizer({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.territory,
    required this.account,
    required this.merchandizerPermissions,
    required this.managerId,
    required this.managerName,
    required this.assignedStores,
    required this.createdAt,
    required this.updatedAt,
  });

  String get fullName => '$firstName $lastName';

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'email': email,
      'phone': phone,
      'territory': territory,
      'account': account.toMap(),
      'merchandizerPermissions': merchandizerPermissions.toMap(),
      'managerId': managerId,
      'managerName': managerName,
      'assignedStores': assignedStores,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  factory Merchandizer.fromMap(Map<String, dynamic> map) {
    return Merchandizer(
      id: map['id'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      territory: map['territory'] ?? '',
      account: UserAccount.fromMap(map['account'] ?? {}),
      merchandizerPermissions: MerchandizerPermissions.fromMap(map['merchandizerPermissions'] ?? {}),
      managerId: map['managerId'] ?? '',
      managerName: map['managerName'] ?? '',
      assignedStores: List<String>.from(map['assignedStores'] ?? []),
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Merchandizer copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? territory,
    UserAccount? account,
    MerchandizerPermissions? merchandizerPermissions,
    String? managerId,
    String? managerName,
    List<String>? assignedStores,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Merchandizer(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      territory: territory ?? this.territory,
      account: account ?? this.account,
      merchandizerPermissions: merchandizerPermissions ?? this.merchandizerPermissions,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      assignedStores: assignedStores ?? this.assignedStores,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

// Merchandizer Permissions Model
class MerchandizerPermissions {
  final int maxOfflineDays;
  final bool requireBiometric;

  const MerchandizerPermissions({
    required this.maxOfflineDays,
    required this.requireBiometric,
  });

  Map<String, dynamic> toMap() {
    return {
      'maxOfflineDays': maxOfflineDays,
      'requireBiometric': requireBiometric,
    };
  }

  factory MerchandizerPermissions.fromMap(Map<String, dynamic> map) {
    return MerchandizerPermissions(
      maxOfflineDays: map['maxOfflineDays'] ?? AppConstants.defaultMaxOfflineDays,
      requireBiometric: map['requireBiometric'] ?? false,
    );
  }
}

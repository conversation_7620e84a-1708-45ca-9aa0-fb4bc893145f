import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/business_models.dart' as business;
import '../services/firebase/firebase_service.dart';
import '../services/product_service.dart';

// Product service provider
final productServiceProvider = Provider<ProductService>((ref) {
  return ProductService();
});

// All products stream provider
final productsStreamProvider = StreamProvider<List<business.Product>>((ref) async* {
  final stream = FirebaseService.collectionStream(
    FirebaseService.productsCollection,
    queryBuilder: (query) => query
        .where('isActive', isEqualTo: true)
        .orderBy('name'),
  );

  await for (final snapshot in stream) {
    final products = snapshot.docs
        .map((doc) => business.Product.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield products;
  }
});

// Products by category provider
final productsByCategoryProvider = Provider.family<List<business.Product>, String>((ref, category) {
  final products = ref.watch(productsStreamProvider);
  return products.when(
    data: (productsList) => productsList.where((product) => product.category == category).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Low stock products provider
final lowStockProductsProvider = Provider<List<business.Product>>((ref) {
  final products = ref.watch(productsStreamProvider);
  return products.when(
    data: (productsList) => productsList.where((product) => 
        product.stockQuantity <= product.minStockLevel).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Product categories provider
final productCategoriesProvider = Provider<List<String>>((ref) {
  final products = ref.watch(productsStreamProvider);
  return products.when(
    data: (productsList) {
      final categories = productsList.map((product) => product.category).toSet().toList();
      categories.sort();
      return categories;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Single product provider
final productProvider = StreamProvider.family<business.Product?, String>((ref, productId) async* {
  final stream = FirebaseService.documentStream(
    FirebaseService.productsCollection.doc(productId),
  );

  await for (final snapshot in stream) {
    if (snapshot.exists) {
      yield business.Product.fromMap(snapshot.data() as Map<String, dynamic>);
    } else {
      yield null;
    }
  }
});

// Product search provider
final productSearchProvider = StateProvider<String>((ref) => '');

// Filtered products provider
final filteredProductsProvider = Provider<List<business.Product>>((ref) {
  final products = ref.watch(productsStreamProvider);
  final searchQuery = ref.watch(productSearchProvider).toLowerCase();
  
  return products.when(
    data: (productsList) {
      if (searchQuery.isEmpty) {
        return productsList;
      }
      
      return productsList.where((product) =>
          product.name.toLowerCase().contains(searchQuery) ||
          product.description.toLowerCase().contains(searchQuery) ||
          product.category.toLowerCase().contains(searchQuery) ||
          product.sku.toLowerCase().contains(searchQuery)
      ).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Inventory statistics provider
final inventoryStatisticsProvider = Provider<InventoryStatistics>((ref) {
  final products = ref.watch(productsStreamProvider);
  return products.when(
    data: (productsList) => InventoryStatistics.calculate(productsList),
    loading: () => InventoryStatistics.empty(),
    error: (_, __) => InventoryStatistics.empty(),
  );
});

// Product creation state provider
final productCreationProvider = StateNotifierProvider<ProductCreationNotifier, ProductCreationState>((ref) {
  final productService = ref.watch(productServiceProvider);
  return ProductCreationNotifier(productService);
});

// Product creation state
class ProductCreationState {
  final bool isLoading;
  final String? error;
  final business.Product? createdProduct;

  const ProductCreationState({
    this.isLoading = false,
    this.error,
    this.createdProduct,
  });

  ProductCreationState copyWith({
    bool? isLoading,
    String? error,
    business.Product? createdProduct,
  }) {
    return ProductCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdProduct: createdProduct ?? this.createdProduct,
    );
  }
}

// Product creation notifier
class ProductCreationNotifier extends StateNotifier<ProductCreationState> {
  final ProductService _productService;

  ProductCreationNotifier(this._productService) : super(const ProductCreationState());

  Future<void> createProduct({
    required String name,
    required String description,
    required String category,
    required String sku,
    required double price,
    required int stockQuantity,
    required int minStockLevel,
    String? imageUrl,
    Map<String, dynamic>? specifications,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final product = await _productService.createProduct(
        name: name,
        description: description,
        category: category,
        sku: sku,
        price: price,
        stockQuantity: stockQuantity,
        minStockLevel: minStockLevel,
        imageUrl: imageUrl,
        specifications: specifications,
      );

      state = state.copyWith(
        isLoading: false,
        createdProduct: product,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  void reset() {
    state = const ProductCreationState();
  }
}

// Inventory statistics model
class InventoryStatistics {
  final int totalProducts;
  final int activeProducts;
  final int lowStockProducts;
  final int outOfStockProducts;
  final double totalInventoryValue;
  final double averageProductPrice;
  final int totalStockQuantity;

  const InventoryStatistics({
    required this.totalProducts,
    required this.activeProducts,
    required this.lowStockProducts,
    required this.outOfStockProducts,
    required this.totalInventoryValue,
    required this.averageProductPrice,
    required this.totalStockQuantity,
  });

  factory InventoryStatistics.empty() {
    return const InventoryStatistics(
      totalProducts: 0,
      activeProducts: 0,
      lowStockProducts: 0,
      outOfStockProducts: 0,
      totalInventoryValue: 0.0,
      averageProductPrice: 0.0,
      totalStockQuantity: 0,
    );
  }

  factory InventoryStatistics.calculate(List<business.Product> products) {
    final totalProducts = products.length;
    final activeProducts = products.where((p) => p.isActive).length;
    final lowStockProducts = products.where((p) => 
        p.stockQuantity <= p.minStockLevel && p.stockQuantity > 0).length;
    final outOfStockProducts = products.where((p) => p.stockQuantity == 0).length;
    
    final totalInventoryValue = products.fold<double>(
      0.0,
      (total, product) => total + (product.price * product.stockQuantity),
    );
    
    final averageProductPrice = totalProducts > 0 
        ? products.fold<double>(0.0, (total, product) => total + product.price) / totalProducts
        : 0.0;
    
    final totalStockQuantity = products.fold<int>(
      0,
      (total, product) => total + product.stockQuantity,
    );

    return InventoryStatistics(
      totalProducts: totalProducts,
      activeProducts: activeProducts,
      lowStockProducts: lowStockProducts,
      outOfStockProducts: outOfStockProducts,
      totalInventoryValue: totalInventoryValue,
      averageProductPrice: averageProductPrice,
      totalStockQuantity: totalStockQuantity,
    );
  }
}

// Stock alert provider
final stockAlertsProvider = Provider<List<StockAlert>>((ref) {
  final products = ref.watch(productsStreamProvider);
  return products.when(
    data: (productsList) {
      final alerts = <StockAlert>[];
      
      for (final product in productsList) {
        if (product.stockQuantity == 0) {
          alerts.add(StockAlert(
            product: product,
            type: StockAlertType.outOfStock,
            message: 'Produit en rupture de stock',
          ));
        } else if (product.stockQuantity <= product.minStockLevel) {
          alerts.add(StockAlert(
            product: product,
            type: StockAlertType.lowStock,
            message: 'Stock faible (${product.stockQuantity} restant)',
          ));
        }
      }
      
      return alerts;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

// Stock alert model
class StockAlert {
  final business.Product product;
  final StockAlertType type;
  final String message;

  const StockAlert({
    required this.product,
    required this.type,
    required this.message,
  });
}

enum StockAlertType {
  lowStock,
  outOfStock,
}

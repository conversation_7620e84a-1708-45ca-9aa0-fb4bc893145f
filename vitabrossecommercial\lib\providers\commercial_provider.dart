import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/business_models.dart' as business;
import '../models/user_models.dart';
import '../models/mission_models.dart';
import '../services/firebase/firebase_service.dart';
import 'auth_provider.dart';

// Commercial clients provider
final commercialClientsProvider =
    StreamProvider<List<business.Client>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.clientsCollection,
    queryBuilder: (query) => query
        .where('assignedCommercials', arrayContains: commercialData.id)
        .orderBy('name'),
  );

  await for (final snapshot in stream) {
    final clients = snapshot.docs
        .map((doc) =>
            business.Client.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield clients;
  }
});

// Commercial orders provider
final commercialOrdersProvider =
    StreamProvider<List<business.Order>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.ordersCollection,
    queryBuilder: (query) => query
        .where('commercialId', isEqualTo: commercialData.id)
        .orderBy('createdAt', descending: true),
  );

  await for (final snapshot in stream) {
    final orders = snapshot.docs
        .map(
            (doc) => business.Order.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield orders;
  }
});

// Commercial quotes provider
final commercialQuotesProvider =
    StreamProvider<List<business.Quote>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.quotesCollection,
    queryBuilder: (query) => query
        .where('commercialId', isEqualTo: commercialData.id)
        .orderBy('createdAt', descending: true),
  );

  await for (final snapshot in stream) {
    final quotes = snapshot.docs
        .map(
            (doc) => business.Quote.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield quotes;
  }
});

// Commercial missions provider
final commercialMissionsProvider = StreamProvider<List<Mission>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.missionsCollection,
    queryBuilder: (query) => query
        .where('createdBy', isEqualTo: commercialData.id)
        .orderBy('missionDate', descending: true),
  );

  await for (final snapshot in stream) {
    final missions = snapshot.docs
        .map((doc) => Mission.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield missions;
  }
});

// Commercial merchandizers provider
final commercialMerchandizersProvider =
    StreamProvider<List<Merchandizer>>((ref) async* {
  final commercialData = await ref.watch(commercialDataProvider.future);
  if (commercialData == null) {
    yield [];
    return;
  }

  final stream = FirebaseService.collectionStream(
    FirebaseService.merchandizersCollection,
    queryBuilder: (query) => query
        .where('managerId', isEqualTo: commercialData.id)
        .orderBy('firstName'),
  );

  await for (final snapshot in stream) {
    final merchandizers = snapshot.docs
        .map((doc) => Merchandizer.fromMap(doc.data() as Map<String, dynamic>))
        .toList();
    yield merchandizers;
  }
});

// Performance metrics provider
final commercialPerformanceProvider = Provider<CommercialPerformance>((ref) {
  final orders = ref.watch(commercialOrdersProvider);
  final quotes = ref.watch(commercialQuotesProvider);
  final missions = ref.watch(commercialMissionsProvider);

  return orders.when(
    data: (ordersList) => quotes.when(
      data: (quotesList) => missions.when(
        data: (missionsList) => CommercialPerformance.calculate(
          orders: ordersList,
          quotes: quotesList,
          missions: missionsList,
        ),
        loading: () => CommercialPerformance.empty(),
        error: (_, __) => CommercialPerformance.empty(),
      ),
      loading: () => CommercialPerformance.empty(),
      error: (_, __) => CommercialPerformance.empty(),
    ),
    loading: () => CommercialPerformance.empty(),
    error: (_, __) => CommercialPerformance.empty(),
  );
});

// Recent orders provider (last 10)
final recentOrdersProvider = Provider<List<business.Order>>((ref) {
  final orders = ref.watch(commercialOrdersProvider);
  return orders.when(
    data: (ordersList) => ordersList.take(10).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Recent quotes provider (last 10)
final recentQuotesProvider = Provider<List<business.Quote>>((ref) {
  final quotes = ref.watch(commercialQuotesProvider);
  return quotes.when(
    data: (quotesList) => quotesList.take(10).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Today's missions provider
final todayMissionsProvider = Provider<List<Mission>>((ref) {
  final missions = ref.watch(commercialMissionsProvider);
  final today = DateTime.now();

  return missions.when(
    data: (missionsList) => missionsList.where((mission) {
      return mission.missionDate.year == today.year &&
          mission.missionDate.month == today.month &&
          mission.missionDate.day == today.day;
    }).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Pending missions provider
final pendingMissionsProvider = Provider<List<Mission>>((ref) {
  final missions = ref.watch(commercialMissionsProvider);

  return missions.when(
    data: (missionsList) => missionsList.where((mission) {
      return mission.status == MissionStatus.pending;
    }).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// Performance metrics model
class CommercialPerformance {
  final int totalOrders;
  final double totalRevenue;
  final int totalQuotes;
  final double quoteConversionRate;
  final int totalMissions;
  final int completedMissions;
  final double missionCompletionRate;
  final int activeClients;
  final double averageOrderValue;

  const CommercialPerformance({
    required this.totalOrders,
    required this.totalRevenue,
    required this.totalQuotes,
    required this.quoteConversionRate,
    required this.totalMissions,
    required this.completedMissions,
    required this.missionCompletionRate,
    required this.activeClients,
    required this.averageOrderValue,
  });

  factory CommercialPerformance.empty() {
    return const CommercialPerformance(
      totalOrders: 0,
      totalRevenue: 0.0,
      totalQuotes: 0,
      quoteConversionRate: 0.0,
      totalMissions: 0,
      completedMissions: 0,
      missionCompletionRate: 0.0,
      activeClients: 0,
      averageOrderValue: 0.0,
    );
  }

  factory CommercialPerformance.calculate({
    required List<business.Order> orders,
    required List<business.Quote> quotes,
    required List<Mission> missions,
  }) {
    final totalOrders = orders.length;
    final totalRevenue = orders.fold<double>(
      0.0,
      (total, order) => total + order.totalAmount,
    );

    final totalQuotes = quotes.length;
    final convertedQuotes = quotes.where((q) => q.convertedToOrder).length;
    final quoteConversionRate =
        totalQuotes > 0 ? (convertedQuotes / totalQuotes) * 100 : 0.0;

    final totalMissions = missions.length;
    final completedMissions =
        missions.where((m) => m.status == MissionStatus.completed).length;
    final missionCompletionRate =
        totalMissions > 0 ? (completedMissions / totalMissions) * 100 : 0.0;

    final uniqueClients = orders.map((o) => o.clientId).toSet().length;
    final averageOrderValue =
        totalOrders > 0 ? totalRevenue / totalOrders : 0.0;

    return CommercialPerformance(
      totalOrders: totalOrders,
      totalRevenue: totalRevenue,
      totalQuotes: totalQuotes,
      quoteConversionRate: quoteConversionRate,
      totalMissions: totalMissions,
      completedMissions: completedMissions,
      missionCompletionRate: missionCompletionRate,
      activeClients: uniqueClients,
      averageOrderValue: averageOrderValue,
    );
  }
}

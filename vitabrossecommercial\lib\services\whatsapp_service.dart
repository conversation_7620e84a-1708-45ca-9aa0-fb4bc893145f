import 'dart:io';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import '../models/business_models.dart' as business;
import '../services/pdf_service.dart';
import '../services/quote_service.dart';

class WhatsAppService {
  final PdfService _pdfService = PdfService();
  final QuoteService _quoteService = QuoteService();

  // Send quote via WhatsApp with PDF attachment
  Future<bool> sendQuoteViaWhatsApp({
    required business.Quote quote,
    String? customMessage,
  }) async {
    try {
      // Generate PDF
      final pdfBytes = await _pdfService.generateQuotePdf(quote);

      // Save PDF to temporary directory
      final tempDir = await getTemporaryDirectory();
      final fileName = 'devis_${quote.quoteNumber}.pdf';
      final pdfFile = File('${tempDir.path}/$fileName');
      await pdfFile.writeAsBytes(pdfBytes);

      // Generate message
      final message = customMessage ?? _generateDefaultMessage(quote);

      // Share via WhatsApp
      final result = await _shareViaWhatsApp(
        phoneNumber: quote.clientWhatsapp,
        message: message,
        filePath: pdfFile.path,
      );

      if (result) {
        // Update quote status to sent
        await _quoteService.updateQuote(quote.id, {
          'whatsappSent': true,
          'whatsappSentDate': DateTime.now(),
          'status': business.QuoteStatus.sent.name,
        });
      }

      return result;
    } catch (e) {
      throw Exception('Erreur lors de l\'envoi WhatsApp: ${e.toString()}');
    }
  }

  // Send quote summary via WhatsApp (text only)
  Future<bool> sendQuoteSummaryViaWhatsApp({
    required business.Quote quote,
    String? customMessage,
  }) async {
    try {
      final message = customMessage ?? _pdfService.generateQuoteSummary(quote);

      final result = await _sendTextViaWhatsApp(
        phoneNumber: quote.clientWhatsapp,
        message: message,
      );

      if (result) {
        // Update quote status
        await _quoteService.updateQuote(quote.id, {
          'whatsappSent': true,
          'whatsappSentDate': DateTime.now(),
          'status': business.QuoteStatus.sent.name,
        });
      }

      return result;
    } catch (e) {
      throw Exception('Erreur lors de l\'envoi WhatsApp: ${e.toString()}');
    }
  }

  // Send order confirmation via WhatsApp
  Future<bool> sendOrderConfirmationViaWhatsApp({
    required business.Order order,
    required String clientWhatsApp,
    String? customMessage,
  }) async {
    try {
      final message = customMessage ?? _generateOrderConfirmationMessage(order);

      return await _sendTextViaWhatsApp(
        phoneNumber: clientWhatsApp,
        message: message,
      );
    } catch (e) {
      throw Exception('Erreur lors de l\'envoi WhatsApp: ${e.toString()}');
    }
  }

  // Send custom message via WhatsApp
  Future<bool> sendCustomMessageViaWhatsApp({
    required String phoneNumber,
    required String message,
    String? filePath,
  }) async {
    try {
      if (filePath != null) {
        return await _shareViaWhatsApp(
          phoneNumber: phoneNumber,
          message: message,
          filePath: filePath,
        );
      } else {
        return await _sendTextViaWhatsApp(
          phoneNumber: phoneNumber,
          message: message,
        );
      }
    } catch (e) {
      throw Exception('Erreur lors de l\'envoi WhatsApp: ${e.toString()}');
    }
  }

  // Check if WhatsApp is installed
  Future<bool> isWhatsAppInstalled() async {
    try {
      final uri = Uri.parse('whatsapp://send');
      return await canLaunchUrl(uri);
    } catch (e) {
      return false;
    }
  }

  // Validate phone number format
  bool isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it's a valid length (between 10 and 15 digits)
    if (cleanNumber.length < 10 || cleanNumber.length > 15) {
      return false;
    }

    // Check if it starts with a valid country code or local format
    return cleanNumber.startsWith('33') || // France
        cleanNumber.startsWith('0') || // Local French format
        cleanNumber.length >= 10; // International format
  }

  // Format phone number for WhatsApp
  String formatPhoneNumberForWhatsApp(String phoneNumber) {
    // Remove all non-digit characters
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Handle French numbers
    if (cleanNumber.startsWith('0')) {
      // Convert local French format (0X XX XX XX XX) to international (+33X XX XX XX XX)
      cleanNumber = '33${cleanNumber.substring(1)}';
    } else if (!cleanNumber.startsWith('33') && cleanNumber.length == 10) {
      // Assume it's a French number without the leading 0
      cleanNumber = '33$cleanNumber';
    }

    return cleanNumber;
  }

  // Private helper methods
  Future<bool> _shareViaWhatsApp({
    required String phoneNumber,
    required String message,
    required String filePath,
  }) async {
    try {
      // Create XFile for sharing
      final xFile = XFile(filePath);

      // Share with WhatsApp
      final result = await Share.shareXFiles(
        [xFile],
        text: message,
        subject: 'Devis VitaBrosse',
      );

      return result.status == ShareResultStatus.success;
    } catch (e) {
      // Fallback: try to open WhatsApp with text only
      return await _sendTextViaWhatsApp(
        phoneNumber: phoneNumber,
        message: '$message\n\n(Le fichier PDF sera envoyé séparément)',
      );
    }
  }

  Future<bool> _sendTextViaWhatsApp({
    required String phoneNumber,
    required String message,
  }) async {
    try {
      // Format phone number
      final formattedNumber = formatPhoneNumberForWhatsApp(phoneNumber);

      // Encode message for URL
      final encodedMessage = Uri.encodeComponent(message);

      // Create WhatsApp URL
      final whatsappUrl = 'https://wa.me/$formattedNumber?text=$encodedMessage';
      final uri = Uri.parse(whatsappUrl);

      // Launch WhatsApp
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        throw Exception('WhatsApp n\'est pas installé sur cet appareil');
      }
    } catch (e) {
      throw Exception('Impossible d\'ouvrir WhatsApp: ${e.toString()}');
    }
  }

  String _generateDefaultMessage(business.Quote quote) {
    return '''Bonjour ${quote.clientName},

J'espère que vous allez bien. Je vous transmets votre devis ${quote.quoteNumber} pour les produits VitaBrosse que nous avons sélectionnés ensemble.

📋 **Détails du devis :**
• Numéro : ${quote.quoteNumber}
• Montant total : ${quote.totalAmount.toStringAsFixed(2)}€ TTC
• Valable jusqu'au : ${_formatDate(quote.validUntil)}

Le devis détaillé est joint à ce message en PDF.

N'hésitez pas à me contacter si vous avez des questions ou si vous souhaitez discuter de certains points.

Cordialement,
${quote.commercialName}
VitaBrosse - Solutions de brossage professionnel''';
  }

  String _generateOrderConfirmationMessage(business.Order order) {
    return '''Bonjour,

Votre commande ${order.orderNumber} a été confirmée avec succès !

📦 **Détails de la commande :**
• Numéro : ${order.orderNumber}
• Montant total : ${order.totalAmount.toStringAsFixed(2)}€ TTC
• Date de commande : ${_formatDate(order.orderDate)}
${order.deliveryDate != null ? '• Livraison prévue : ${_formatDate(order.deliveryDate!)}' : ''}

Nous préparons votre commande et vous tiendrons informé de son avancement.

Merci pour votre confiance !

Cordialement,
${order.commercialName}
VitaBrosse - Solutions de brossage professionnel''';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import '../models/business_models.dart' as business;

class PdfService {
  // Generate quote PDF
  Future<Uint8List> generateQuotePdf(business.Quote quote) async {
    final pdf = pw.Document();

    // Load company logo (if available)
    pw.ImageProvider? logoImage;
    try {
      logoImage =
          await imageFromAssetBundle('assets/logos/vitabrosse_logo.png');
    } catch (e) {
      // Logo not found, continue without it
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader(logoImage),
        footer: (context) => _buildFooter(context),
        build: (context) => [
          _buildQuoteInfo(quote),
          pw.SizedBox(height: 20),
          _buildClientInfo(quote),
          pw.SizedBox(height: 20),
          _buildLineItemsTable(quote),
          pw.SizedBox(height: 20),
          _buildTotalsSection(quote),
          pw.SizedBox(height: 20),
          _buildNotesSection(quote),
          pw.SizedBox(height: 20),
          _buildTermsAndConditions(),
        ],
      ),
    );

    return pdf.save();
  }

  // Save PDF to device
  Future<File> savePdfToDevice(Uint8List pdfBytes, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(pdfBytes);
    return file;
  }

  // Share PDF
  Future<void> sharePdf(Uint8List pdfBytes, String fileName) async {
    await Printing.sharePdf(
      bytes: pdfBytes,
      filename: fileName,
    );
  }

  // Print PDF
  Future<void> printPdf(Uint8List pdfBytes) async {
    await Printing.layoutPdf(
      onLayout: (format) async => pdfBytes,
    );
  }

  // Build header section
  pw.Widget _buildHeader(pw.ImageProvider? logoImage) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: PdfColors.grey300,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              if (logoImage != null)
                pw.Image(logoImage, width: 120, height: 60)
              else
                pw.Text(
                  'VitaBrosse',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    color: PdfColors.blue800,
                  ),
                ),
              pw.SizedBox(height: 8),
              pw.Text(
                'Solutions de brossage professionnel',
                style: const pw.TextStyle(
                  fontSize: 12,
                  color: PdfColors.grey600,
                ),
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'DEVIS',
                style: pw.TextStyle(
                  fontSize: 28,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build footer section
  pw.Widget _buildFooter(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(
            color: PdfColors.grey300,
            width: 1,
          ),
        ),
      ),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text(
                'VitaBrosse - Solutions de brossage professionnel',
                style:
                    const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
              ),
              pw.Text(
                'Page ${context.pageNumber}/${context.pagesCount}',
                style:
                    const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
              ),
            ],
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            'Adresse: 123 Rue de l\'Industrie, 75000 Paris | Tél: +33 1 23 45 67 89 | Email: <EMAIL>',
            style: const pw.TextStyle(fontSize: 8, color: PdfColors.grey500),
          ),
        ],
      ),
    );
  }

  // Build quote information section
  pw.Widget _buildQuoteInfo(business.Quote quote) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text(
              'Numéro de devis: ${quote.quoteNumber}',
              style: pw.TextStyle(
                fontSize: 14,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            pw.SizedBox(height: 4),
            pw.Text(
              'Date: ${_formatDate(quote.quoteDate)}',
              style: const pw.TextStyle(fontSize: 12),
            ),
            pw.Text(
              'Valable jusqu\'au: ${_formatDate(quote.validUntil)}',
              style: const pw.TextStyle(fontSize: 12),
            ),
          ],
        ),
        pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.end,
          children: [
            pw.Text(
              'Commercial: ${quote.commercialName}',
              style: const pw.TextStyle(fontSize: 12),
            ),
            pw.SizedBox(height: 4),
            pw.Container(
              padding:
                  const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: pw.BoxDecoration(
                color: _getStatusColor(quote.status),
                borderRadius: pw.BorderRadius.circular(4),
              ),
              child: pw.Text(
                _getStatusText(quote.status),
                style: const pw.TextStyle(
                  fontSize: 10,
                  color: PdfColors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Build client information section
  pw.Widget _buildClientInfo(business.Quote quote) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'INFORMATIONS CLIENT',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue800,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            quote.clientName,
            style: pw.TextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          // Add more client details here if available
        ],
      ),
    );
  }

  // Build line items table
  pw.Widget _buildLineItemsTable(business.Quote quote) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'DÉTAIL DES ARTICLES',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FlexColumnWidth(3),
            1: const pw.FlexColumnWidth(1),
            2: const pw.FlexColumnWidth(1.5),
            3: const pw.FlexColumnWidth(1.5),
            4: const pw.FlexColumnWidth(1.5),
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildTableCell('Article', isHeader: true),
                _buildTableCell('Qté', isHeader: true),
                _buildTableCell('Prix unitaire', isHeader: true),
                _buildTableCell('Remise', isHeader: true),
                _buildTableCell('Total', isHeader: true),
              ],
            ),
            // Data rows
            ...quote.lineItems.map((item) => pw.TableRow(
                  children: [
                    _buildTableCell(item.productName),
                    _buildTableCell(item.quantity.toString()),
                    _buildTableCell('${item.unitPrice.toStringAsFixed(2)}€'),
                    _buildTableCell(item.discount > 0
                        ? '-${item.discount.toStringAsFixed(2)}€'
                        : '-'),
                    _buildTableCell('${item.total.toStringAsFixed(2)}€'),
                  ],
                )),
          ],
        ),
      ],
    );
  }

  // Build table cell
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }

  // Build totals section
  pw.Widget _buildTotalsSection(business.Quote quote) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.end,
      children: [
        pw.Container(
          width: 200,
          child: pw.Column(
            children: [
              _buildTotalRow(
                  'Sous-total:', '${quote.subtotal.toStringAsFixed(2)}€'),
              if (quote.discountTotal > 0)
                _buildTotalRow(
                    'Remise:', '-${quote.discountTotal.toStringAsFixed(2)}€'),
              _buildTotalRow(
                  'TVA (20%):', '${quote.taxTotal.toStringAsFixed(2)}€'),
              pw.Divider(color: PdfColors.grey400),
              _buildTotalRow(
                'TOTAL:',
                '${quote.totalAmount.toStringAsFixed(2)}€',
                isTotal: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build total row
  pw.Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: isTotal ? 14 : 12,
              fontWeight: isTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: isTotal ? PdfColors.blue800 : PdfColors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Build notes section
  pw.Widget _buildNotesSection(business.Quote quote) {
    if (quote.notes.isEmpty) return pw.SizedBox();

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'NOTES',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            color: PdfColors.grey50,
            borderRadius: pw.BorderRadius.circular(4),
          ),
          child: pw.Text(
            quote.notes,
            style: const pw.TextStyle(fontSize: 11),
          ),
        ),
      ],
    );
  }

  // Build terms and conditions
  pw.Widget _buildTermsAndConditions() {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'CONDITIONS GÉNÉRALES',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          '• Ce devis est valable pour la durée indiquée ci-dessus.\n'
          '• Les prix sont exprimés en euros TTC.\n'
          '• Paiement à 30 jours fin de mois.\n'
          '• Livraison sous 15 jours ouvrés après acceptation du devis.\n'
          '• Garantie constructeur selon conditions du fabricant.',
          style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey700),
        ),
      ],
    );
  }

  // Generate quote summary for sharing
  String generateQuoteSummary(business.Quote quote) {
    final buffer = StringBuffer();
    buffer.writeln('📋 *DEVIS ${quote.quoteNumber}*');
    buffer.writeln('');
    buffer.writeln('👤 Client: ${quote.clientName}');
    buffer.writeln('📅 Date: ${_formatDate(quote.quoteDate)}');
    buffer.writeln('⏰ Valable jusqu\'au: ${_formatDate(quote.validUntil)}');
    buffer.writeln('👨‍💼 Commercial: ${quote.commercialName}');
    buffer.writeln('');
    buffer.writeln('📦 *Articles:*');

    for (final item in quote.lineItems) {
      buffer.writeln('• ${item.productName}');
      buffer.writeln(
          '  Qté: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)}€');
      if (item.discount > 0) {
        buffer.writeln('  Remise: -${item.discount.toStringAsFixed(2)}€');
      }
      buffer.writeln('  Total: ${item.total.toStringAsFixed(2)}€');
      buffer.writeln('');
    }

    buffer.writeln('💰 *Récapitulatif:*');
    buffer.writeln('Sous-total: ${quote.subtotal.toStringAsFixed(2)}€');
    if (quote.discountTotal > 0) {
      buffer.writeln('Remise: -${quote.discountTotal.toStringAsFixed(2)}€');
    }
    buffer.writeln('TVA (20%): ${quote.taxTotal.toStringAsFixed(2)}€');
    buffer.writeln('*TOTAL: ${quote.totalAmount.toStringAsFixed(2)}€*');

    if (quote.notes.isNotEmpty) {
      buffer.writeln('');
      buffer.writeln('📝 *Notes:*');
      buffer.writeln(quote.notes);
    }

    buffer.writeln('');
    buffer.writeln('---');
    buffer.writeln('VitaBrosse - Solutions de brossage professionnel');

    return buffer.toString();
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  PdfColor _getStatusColor(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return PdfColors.orange;
      case business.QuoteStatus.sent:
        return PdfColors.blue;
      case business.QuoteStatus.viewed:
        return PdfColors.purple;
      case business.QuoteStatus.accepted:
        return PdfColors.green;
      case business.QuoteStatus.rejected:
        return PdfColors.red;
      case business.QuoteStatus.expired:
        return PdfColors.grey;
    }
  }

  String _getStatusText(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return 'En attente';
      case business.QuoteStatus.sent:
        return 'Envoyé';
      case business.QuoteStatus.viewed:
        return 'Vu';
      case business.QuoteStatus.accepted:
        return 'Accepté';
      case business.QuoteStatus.rejected:
        return 'Refusé';
      case business.QuoteStatus.expired:
        return 'Expiré';
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';

class QuotesScreen extends ConsumerWidget {
  const QuotesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Devis'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              // TODO: Navigate to create quote
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description,
              size: 64,
              color: Color(AppColors.secondaryColorValue),
            ),
            SizedBox(height: 16),
            Text(
              'Écran des devis',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text(
              'À implémenter dans la prochaine tâche',
              style: TextStyle(
                color: Color(AppColors.secondaryTextColorValue),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

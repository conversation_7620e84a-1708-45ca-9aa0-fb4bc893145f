import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../models/business_models.dart' as business;
import '../../providers/quote_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/cards/performance_card.dart';
import 'create_quote_screen.dart';
import 'quote_details_screen.dart';

class QuotesScreen extends ConsumerStatefulWidget {
  const QuotesScreen({super.key});

  @override
  ConsumerState<QuotesScreen> createState() => _QuotesScreenState();
}

class _QuotesScreenState extends ConsumerState<QuotesScreen> {
  final _searchController = TextEditingController();
  String _searchQuery = '';
  business.QuoteStatus? _selectedStatus;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final quotesAsync = ref.watch(quotesStreamProvider);
    final quoteStats = ref.watch(quoteStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Devis'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateQuoteScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search and Filter Section
          Container(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              children: [
                // Search Bar
                CustomSearchField(
                  controller: _searchController,
                  hint: 'Rechercher un devis...',
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),

                const SizedBox(height: 12),

                // Status Filter
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildFilterChip(
                        'Tous',
                        _selectedStatus == null,
                        () => setState(() => _selectedStatus = null),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'En attente',
                        _selectedStatus == business.QuoteStatus.pending,
                        () => setState(() =>
                            _selectedStatus = business.QuoteStatus.pending),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Envoyés',
                        _selectedStatus == business.QuoteStatus.sent,
                        () => setState(
                            () => _selectedStatus = business.QuoteStatus.sent),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Vus',
                        _selectedStatus == business.QuoteStatus.viewed,
                        () => setState(() =>
                            _selectedStatus = business.QuoteStatus.viewed),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Acceptés',
                        _selectedStatus == business.QuoteStatus.accepted,
                        () => setState(() =>
                            _selectedStatus = business.QuoteStatus.accepted),
                      ),
                      const SizedBox(width: 8),
                      _buildFilterChip(
                        'Refusés',
                        _selectedStatus == business.QuoteStatus.rejected,
                        () => setState(() =>
                            _selectedStatus = business.QuoteStatus.rejected),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Quotes List
          Expanded(
            child: quotesAsync.when(
              data: (quotes) {
                final filteredQuotes = _filterQuotes(quotes);

                if (filteredQuotes.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(quotesStreamProvider);
                  },
                  child: Column(
                    children: [
                      // Stats Summary
                      Container(
                        padding:
                            const EdgeInsets.all(AppConstants.defaultPadding),
                        child: Row(
                          children: [
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Total',
                                value: quoteStats.totalQuotes.toString(),
                                icon: Icons.description,
                                color: const Color(AppColors.primaryColorValue),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CompactMetricCard(
                                title: 'En attente',
                                value: quoteStats.pendingQuotes.toString(),
                                icon: Icons.schedule,
                                color: const Color(AppColors.warningColorValue),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: CompactMetricCard(
                                title: 'Taux conversion',
                                value:
                                    '${quoteStats.conversionRate.toStringAsFixed(1)}%',
                                icon: Icons.trending_up,
                                color: const Color(AppColors.successColorValue),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Quotes List
                      Expanded(
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.defaultPadding,
                          ),
                          itemCount: filteredQuotes.length,
                          itemBuilder: (context, index) {
                            final quote = filteredQuotes[index];
                            return _buildQuoteCard(context, quote);
                          },
                        ),
                      ),
                    ],
                  ),
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Color(AppColors.errorColorValue),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Erreur de chargement',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error.toString(),
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        ref.invalidate(quotesStreamProvider);
                      },
                      child: const Text('Réessayer'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<business.Quote> _filterQuotes(List<business.Quote> quotes) {
    return quotes.where((quote) {
      final matchesSearch = _searchQuery.isEmpty ||
          quote.quoteNumber.toLowerCase().contains(_searchQuery) ||
          quote.clientName.toLowerCase().contains(_searchQuery);

      final matchesStatus =
          _selectedStatus == null || quote.status == _selectedStatus;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor:
          const Color(AppColors.primaryColorValue).withValues(alpha: 0.2),
      checkmarkColor: const Color(AppColors.primaryColorValue),
    );
  }

  Widget _buildQuoteCard(BuildContext context, business.Quote quote) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => QuoteDetailsScreen(quoteId: quote.id),
            ),
          );
        },
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          _getStatusColor(quote.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getStatusIcon(quote.status),
                      color: _getStatusColor(quote.status),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          quote.quoteNumber,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          quote.clientName,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: const Color(
                                        AppColors.secondaryTextColorValue),
                                  ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${quote.totalAmount.toStringAsFixed(2)}€',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(AppColors.primaryColorValue),
                            ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(quote.status)
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getStatusText(quote.status),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: _getStatusColor(quote.status),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${quote.quoteDate.day}/${quote.quoteDate.month}/${quote.quoteDate.year}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Expire le ${quote.validUntil.day}/${quote.validUntil.month}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.description,
                    size: 16,
                    color: const Color(AppColors.secondaryTextColorValue),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${quote.lineItems.length} article(s)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(AppColors.secondaryTextColorValue),
                        ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun devis trouvé',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: const Color(AppColors.secondaryTextColorValue),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Créez votre premier devis',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: const Color(AppColors.secondaryTextColorValue),
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const CreateQuoteScreen(),
                ),
              );
            },
            icon: const Icon(Icons.add),
            label: const Text('Créer un devis'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return const Color(AppColors.warningColorValue);
      case business.QuoteStatus.sent:
        return const Color(AppColors.infoColorValue);
      case business.QuoteStatus.viewed:
        return const Color(AppColors.primaryColorValue);
      case business.QuoteStatus.accepted:
        return const Color(AppColors.successColorValue);
      case business.QuoteStatus.rejected:
        return const Color(AppColors.errorColorValue);
      case business.QuoteStatus.expired:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return Icons.schedule;
      case business.QuoteStatus.sent:
        return Icons.send;
      case business.QuoteStatus.viewed:
        return Icons.visibility;
      case business.QuoteStatus.accepted:
        return Icons.check_circle;
      case business.QuoteStatus.rejected:
        return Icons.cancel;
      case business.QuoteStatus.expired:
        return Icons.access_time_filled;
    }
  }

  String _getStatusText(business.QuoteStatus status) {
    switch (status) {
      case business.QuoteStatus.pending:
        return 'En attente';
      case business.QuoteStatus.sent:
        return 'Envoyé';
      case business.QuoteStatus.viewed:
        return 'Vu';
      case business.QuoteStatus.accepted:
        return 'Accepté';
      case business.QuoteStatus.rejected:
        return 'Refusé';
      case business.QuoteStatus.expired:
        return 'Expiré';
    }
  }
}

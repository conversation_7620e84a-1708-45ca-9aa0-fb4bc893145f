import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_button.dart';
import 'change_password_screen.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedCredentials() async {
    final authController = ref.read(authControllerProvider.notifier);
    final savedCredentials = await authController.getSavedCredentials();

    if (mounted) {
      setState(() {
        _usernameController.text = savedCredentials['username'] ?? '';
        _rememberMe = savedCredentials['rememberMe'] ?? false;
      });
    }
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    await ref.read(authControllerProvider.notifier).signInWithUsername(
          username: _usernameController.text.trim(),
          password: _passwordController.text,
          rememberMe: _rememberMe,
        );
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authControllerProvider);
    final isLoading = authState is AuthLoading;

    // Listen to auth state changes
    ref.listen<AuthState>(authControllerProvider, (previous, next) {
      if (next is AuthAuthenticated) {
        // Navigate to dashboard
        Navigator.of(context).pushReplacementNamed('/dashboard');
      } else if (next is AuthPasswordChangeRequired) {
        // Navigate to password change screen
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const ChangePasswordScreen(isRequired: true),
          ),
        );
      } else if (next is AuthError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: const Color(AppColors.errorColorValue),
          ),
        );
      }
    });

    return Scaffold(
      backgroundColor: const Color(AppColors.backgroundColorValue),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.largePadding),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 60),

                // Logo and Title
                Center(
                  child: Column(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: const Color(AppColors.primaryColorValue),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.business_center,
                          size: 50,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 20),
                      const Text(
                        AppConstants.appName,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(AppColors.primaryTextColorValue),
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Connexion sécurisée',
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(AppColors.secondaryTextColorValue),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 50),

                // Username Field
                CustomTextField(
                  controller: _usernameController,
                  label: AppStrings.usernameLabel,
                  prefixIcon: Icons.person_outline,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppStrings.requiredField;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Password Field
                CustomTextField(
                  controller: _passwordController,
                  label: AppStrings.passwordLabel,
                  prefixIcon: Icons.lock_outline,
                  obscureText: _obscurePassword,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppStrings.requiredField;
                    }
                    if (value.length < AppConstants.minPasswordLength) {
                      return AppStrings.passwordTooShort;
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Remember Me Checkbox
                Row(
                  children: [
                    Checkbox(
                      value: _rememberMe,
                      onChanged: (value) {
                        setState(() {
                          _rememberMe = value ?? false;
                        });
                      },
                      activeColor: const Color(AppColors.primaryColorValue),
                    ),
                    const Text(
                      'Se souvenir de moi',
                      style: TextStyle(
                        color: Color(AppColors.secondaryTextColorValue),
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {
                        // TODO: Implement forgot password
                      },
                      child: const Text(AppStrings.forgotPassword),
                    ),
                  ],
                ),

                const SizedBox(height: 30),

                // Login Button
                LoadingButton(
                  onPressed: _handleLogin,
                  isLoading: isLoading,
                  text: AppStrings.loginButton,
                ),

                const SizedBox(height: 40),

                // Support Information
                Container(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius:
                        BorderRadius.circular(AppConstants.defaultBorderRadius),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Color(AppColors.infoColorValue),
                        size: 24,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Compte créé par l\'administrateur',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Color(AppColors.infoColorValue),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Contactez votre administrateur pour obtenir vos identifiants de connexion.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(AppColors.secondaryTextColorValue),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Support: ${AppConstants.supportEmail}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(AppColors.infoColorValue),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
